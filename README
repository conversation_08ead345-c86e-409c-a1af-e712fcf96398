# Monexa Back
## Running server locally

### infrastructure
**Command to run rabbitmq and redis**
`(cd infrastructure && docker compose -f local/docker-compose.yml up --build -d  rabbitmq redis)`

### auth service
**Command to run auth-db**

`(cd services/auth-service && docker compose -f build/local/docker-compose.yml up --build -d  auth_postgres_db_local)`

**Command to run auth service**

`(cd services/auth-service && go run cmd/api/main.go)`

**Command to run auth seed**

`(cd services/auth-service && go run cmd/seeder/main.go)`

### transaction service
**Command to run transaction-db**

`(cd services/transaction-service && docker compose -f build/local/docker-compose.yml up --build -d  transaction_postgres_db)`

**Command to run transaction service**

`(cd services/transaction-service && go run cmd/api/main.go**)`

**Command to run transaction seed**

`(cd services/transaction-service && go run cmd/seeder/main.go)`

**Command to install wkhtmltopdf for pdf management**
`apt-get -y install wkhtmltopdf`