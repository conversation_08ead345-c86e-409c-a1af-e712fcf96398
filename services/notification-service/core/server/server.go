package server

import (
	"context"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/mongo"
	"log"
	"net/http"
	"notification-service/core/config"
	"notification-service/core/connect"
	"notification-service/core/database"
	"notification-service/core/rabbitmq"
	"notification-service/core/redis"
	"notification-service/core/worker"
	"notification-service/core/ws_manger"
	"notification-service/internal/repositories"
	"notification-service/internal/services"
	"notification-service/pkg/logger"
)

type Server struct {
	Srv  *http.Server
	Test bool
}

func (s *Server) Init() (*gin.Engine, *worker.RabbitMQHandler, error) {
	conf, err := config.Load()
	if err != nil {
		fmt.Printf("err config.Load() %s\n", err)
		return nil, nil, err
	}
	logger.Init("app.log")
	//init redis
	rds := redis.New(conf.Redis)
	mongoClient, err := database.ConnectMongo(conf.Database)
	if err != nil {
		return nil, nil, err
	}

	//init entities
	repo := repositories.NewRepository(mongoClient, rds)
	service := services.NewService(repo, *conf)

	//init rabbitmq
	rab := rabbitmq.NewRabbitMq()
	err = rab.Init(conf.RabbitMQ)
	if err != nil {
		fmt.Println(err.Error())
		fmt.Printf("err rabbitmq connect %s\n", err)
	}

	//init real logger
	loggerr, err := logger.NewLogger(config.Get().Dir.LogPath)
	if err != nil {
		fmt.Printf("error init log zap")
	}
	defer loggerr.Sync()
	connect.Upgrade = &websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		// If stream is big use it, if not default is fine.
		//ReadBufferSize:  4096,
		//WriteBufferSize: 4096,
	}
	rabHandler := worker.NewRabbitMQHandler(service, rab, loggerr)
	connect.WsManager = ws_manger.NewWsManager()

	connect.MongoDB = mongoClient
	connect.RabbitMQ = rab
	connect.Logger = loggerr
	connect.Redis = rds
	//routing api
	r := SetupRoutesWithDeps(service)

	return r, rabHandler, nil
}

func (s *Server) Run(r *gin.Engine) {
	if r == nil {
		return
	}

	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", config.Get().Server.Host, config.Get().Server.Port),
		Handler: r,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("run.seeder: %s\n", err)
		}
	}()

	s.Srv = srv
}

func CloseDb(db2 *mongo.Client) {
	err := db2.Disconnect(context.Background())
	if err != nil {
		fmt.Printf("error get mong db close %s\n", err)
		return
	}
}

func (s *Server) CloseAll() {
	d := connect.MongoDB
	CloseDb(d)
}
