package server

import (
	"github.com/elliotchance/orderedmap"
	"github.com/gin-gonic/gin"
	"notification-service/core/connect"
	"notification-service/internal/controllers"
	middleware2 "notification-service/internal/middleware"
	"notification-service/internal/services"
	"notification-service/internal/wshandlers"
)

type Controller interface {
	Register(r *gin.RouterGroup, s string) *gin.RouterGroup
}

func SetupRoutesWithDeps(service *services.Service) *gin.Engine {

	e := gin.Default()
	e.MaxMultipartMemory = 8 << 20
	e.Use(middleware2.CORSMiddleware(), middleware2.Log)
	auth := e.Group("/api/v1")
	{
		r := orderedmap.NewOrderedMap()

		r.Set("ws/user", wshandlers.NewUserHandler(connect.Upgrade, service.UserServiceInterface))
		r.Set("notification", controllers.NewNotificationController(service.NotificationServiceInterface, service.UserServiceInterface))
		for g := r.Front(); g != nil; g = g.Next() {
			if c, ok := g.Value.(Controller); ok {
				c.Register(auth, g.Key.(string))
			}
		}
	}

	//mediaPath := config.Get().Dir.Media
	//e.Static("/media", mediaPath)

	return e
}
