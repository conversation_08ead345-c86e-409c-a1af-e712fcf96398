package ws_manger

import (
	"github.com/gorilla/websocket"
	"notification-service/internal/dto"
	"sync"
)

type WsManager struct {
	ClientConnections map[uint]*websocket.Conn
	AdminConnections  map[uint]*websocket.Conn
	Mu                sync.RWMutex
}

func NewWsManager() *WsManager {
	return &WsManager{
		ClientConnections: make(map[uint]*websocket.Conn),
		AdminConnections:  make(map[uint]*websocket.Conn),
	}
}

func (wm *WsManager) ReadClientConnection(sessionId uint) *websocket.Conn {
	wm.Mu.RLock()
	conn := wm.ClientConnections[sessionId]
	wm.Mu.RUnlock()
	return conn
}

func (wm *WsManager) ReadAdminConnection(userId uint) *websocket.Conn {
	wm.Mu.RLock()
	conn := wm.AdminConnections[userId]
	wm.Mu.RUnlock()
	return conn
}

func (wm *WsManager) NotifyAllAdmins(notify dto.NotificationPayload) error {
	wm.Mu.RLock()
	for v, c := range wm.AdminConnections {
		notify.UserId = v
		if c != nil {
			if err := c.WriteJSON(notify); err != nil {
				return err
			}
		}
	}
	wm.Mu.RUnlock()
	return nil
}

func (wm *WsManager) RemoveClientConnection(sessionId uint) {
	wm.Mu.Lock()
	delete(wm.ClientConnections, sessionId)
	wm.Mu.Unlock()
}

func (wm *WsManager) RemoveAdminConnection(userId uint) {
	wm.Mu.Lock()
	delete(wm.AdminConnections, userId)
	wm.Mu.Unlock()
}

func (wm *WsManager) AddClientConnection(sessionId uint, conn *websocket.Conn) {
	wm.Mu.Lock()
	wm.ClientConnections[sessionId] = conn
	wm.Mu.Unlock()
}

func (wm *WsManager) AddAdminConnection(userId uint, conn *websocket.Conn) {
	wm.Mu.Lock()
	wm.AdminConnections[userId] = conn
	wm.Mu.Unlock()
}
