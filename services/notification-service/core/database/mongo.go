package database

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"notification-service/core/config"
	"time"
)

func GetUri(config config.Database) string {
	return fmt.Sprintf("mongodb://%s:%s@%s:%s", config.Username, config.Password, config.Host, config.Port)
}

func ConnectMongo(config config.Database) (*mongo.Client, error) {
	// Create a MongoClient with the given URI
	client, err := mongo.NewClient(options.Client().ApplyURI(GetUri(config)))
	if err != nil {
		return nil, err
	}

	// Create a context to connect with a timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Connect to MongoDB
	err = client.Connect(ctx)
	if err != nil {
		return nil, err
	}
	if err = client.Ping(ctx, nil); err != nil {
		return nil, err
	}
	// Get a handle to the "transaction" database and "accounts" collection
	return client, nil

}
