package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"notification-service/core/connect"
	rabbitmq2 "notification-service/core/rabbitmq"
	"notification-service/internal/constants"
	"notification-service/internal/dto"
	"notification-service/internal/models"
	services "notification-service/internal/services"
	"time"
)

type RabbitMQHandler struct {
	rab     *rabbitmq2.RabbitMQ
	logger  *zap.Logger
	service *services.Service
}

func NewRabbitMQHandler(service *services.Service, rab *rabbitmq2.RabbitMQ, logger *zap.Logger) *RabbitMQHandler {
	return &RabbitMQHandler{
		rab:     rab,
		logger:  logger,
		service: service,
	}
}

func (rh *RabbitMQHandler) Start(ctx context.Context) error {

	msgs, err := rh.rab.Consume(
		rabbitmq2.UserQueue,
		"consumer",
	)
	if err != nil {
		return fmt.Errorf("failed to register a consumer: %w", err)
	}

	if err != nil {
		return fmt.Errorf("failed to register a consumer: %w", err)
	}
	rh.logger.Info("Worker started", zap.String("queue", "user_queue"))

	go func() {
		for d := range msgs {
			rh.logger.Info("Received a message",
				zap.String("delivery_tag", fmt.Sprintf("%d", d.DeliveryTag)),
				zap.ByteString("body", d.Body),
			)
			var notificationPayload dto.NotificationPayload
			if err = json.Unmarshal(d.Body, &notificationPayload); err != nil {
				rh.logger.Info("failed to parse message in notification consumer",
					zap.String("delivery_tag", fmt.Sprintf("%d", d.DeliveryTag)),
					zap.ByteString("body", d.Body),
				)
				continue
			}
			//TODO save notification
			var notificationModel = models.Notification{
				UserID:      notificationPayload.UserId,
				Type:        notificationPayload.Type,
				Message:     notificationPayload.Message,
				IsDelivered: false,
				Role:        notificationPayload.Role,
				CreatedAt:   time.Now(),
				Transaction: &models.Transaction{},
			}
			if notificationPayload.Transaction != nil {
				notificationModel.Transaction.AccountName = notificationPayload.Transaction.AccountName
				notificationModel.Transaction.Amount = notificationPayload.Transaction.Amount
				notificationModel.Transaction.OperationType = notificationPayload.Transaction.OperationType
				notificationModel.Transaction.Direction = notificationPayload.Transaction.Direction
				notificationModel.Transaction.Currency = notificationPayload.Transaction.Currency
			}

			var conn *websocket.Conn
			if notificationPayload.Role == constants.AdminRole || notificationPayload.Role == constants.SuperAdminRole {
				conn = connect.WsManager.ReadAdminConnection(notificationPayload.UserId)
			} else {
				conn = connect.WsManager.ReadClientConnection(notificationPayload.UserId)
			}
			if err = rh.service.NotificationServiceInterface.Create(&notificationModel); err != nil {
				rh.logger.Error("failed to create notification model in consumer",
					zap.String("delivery_tag", fmt.Sprintf("%d", d.DeliveryTag)),
					zap.String("user_id", fmt.Sprintf("%d", d.DeliveryTag)))
			}
			if conn != nil {
				notificationPayload.ID = notificationModel.ID.Hex()
				notificationPayload.CreatedAt = notificationModel.CreatedAt
				if err = conn.WriteJSON(notificationPayload); err != nil {
					rh.logger.Error("failed to write to ws connection in notification consumer",
						zap.String("delivery_tag", fmt.Sprintf("%d", d.DeliveryTag)),
						zap.ByteString("body", d.Body),
					)
					continue
				}
			}
		}
	}()
	<-ctx.Done()
	rh.logger.Info("Shutting down worker...")
	return nil
}
