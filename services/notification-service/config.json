{"server": {"host": "0.0.0.0", "port": 8091}, "database": {"host": "localhost", "port": 27017, "name": "monexa_db", "username": "root", "password": "example"}, "rabbitMQ": {"host": "localhost", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "token": {"secretKey": "aafsdfwe23rfasdvrg9fvjwfwu4832k2masfgjouergrg"}, "redis": {"address": "localhost:6379", "db": 0, "password": "dfff34#2pl#ghl"}}