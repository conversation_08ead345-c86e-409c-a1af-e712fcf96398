version: '3.7'

services:
  notification_service:
    container_name: notification-service
    build:
      context: ../../
      dockerfile: build/local/Dockerfile
    restart: always
    ports:
      - "8091:8091"
    volumes:
      - ../../logs:/app/logs

    depends_on:
      - notification_mongodb

    command: ["./api"]
    networks:
      - infrastructure_network

  notification_mongodb:
    image: mongo:latest  # Official MongoDB image
    container_name: notification-mongodb
    ports:
      - "27017:27017"  # Expose MongoDB port to the host
    environment:
      MONGO_INITDB_ROOT_USERNAME: root  # Root username for MongoDB
      MONGO_INITDB_ROOT_PASSWORD: example  # Root password for MongoDB
    volumes:
      - mongo_data:/data/db  # Persist MongoDB data across container restarts
    restart: always

    networks:
      - infrastructure_network


  notification-promtail:
    image: grafana/promtail:2.7.1
    container_name: notification-promtail
    volumes:
      - ../../logs:/var/log
      - ./promtail-config.yaml:/etc/promtail/promtail.yaml
    command: -config.file=/etc/promtail/promtail.yaml
    networks:
      - infrastructure_network


volumes:
  mongo_data:
    driver: local


networks:
  infrastructure_network:
    external: true
  default:
    name: monexa_network
