{"server": {"host": "0.0.0.0", "port": 8091}, "database": {"host": "notification-mongodb", "port": 27017, "name": "mongodb", "username": "root", "password": "example"}, "rabbitMQ": {"host": "monexa-rabbitmq", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "token": {"secretKey": "project_secret_key"}, "redis": {"address": "redis:6379", "db": 0, "password": "dfff34#2pl#ghl"}}