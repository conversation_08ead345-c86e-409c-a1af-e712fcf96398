version: '3.7'

services:
  notification_service:
    container_name: notification-service
    build:
      context: ../../
      dockerfile: build/test/Dockerfile
    restart: always
    ports:
      - "8090:8090"
    volumes:
      - ../../logs:/app/logs

    depends_on:
      - notification_mongodb

    command: ["./api"]
    networks:
      - test_infrastructure_network

  notification_mongodb:
    image: mongo:latest  # Official MongoDB image
    container_name: notification-mongodb
    ports:
      - "27017:27017"  # Expose MongoDB port to the host
    environment:
      MONGO_INITDB_ROOT_USERNAME: root  # Root username for MongoDB
      MONGO_INITDB_ROOT_PASSWORD: example  # Root password for MongoDB
    volumes:
      - monexa_mongo_data:/data/db  # Persist MongoDB data across container restarts
    restart: always
    networks:
      - test_infrastructure_network

#  notification-promtail:
#    image: grafana/promtail:2.7.1
#    container_name: promtail
#    volumes:
#      - ../../logs:/var/log
#      - ./promtail-config.yaml:/etc/promtail/promtail.yaml
#    command: -config.file=/etc/promtail/promtail.yaml
#    networks:
#      - test_infrastructure_network


volumes:
  monexa_mongo_data:
    driver: local


networks:
  test_infrastructure_network:
    external: true

