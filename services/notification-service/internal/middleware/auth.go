package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
	"notification-service/core/config"
	"notification-service/internal/constants"
	"notification-service/pkg/security"
)

type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyWriter) Write(b []byte) (int, error) {
	n, err := w.ResponseWriter.Write(b)
	if err == nil {
		w.body.Write(b)
	}
	return n, err
}

type RequestLogData struct {
	Body         json.RawMessage     `json:"body"`
	Params       map[string]string   `json:"params"`
	Query        map[string][]string `json:"query"`
	FormDataBody string
}

// Authentication Middleware
func Authentication(allowedRoles ...string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var tokenString string
		if ctx.Request.URL.Path == "/api/v1/ws/user/connect" {
			tokenString = ctx.Query("token")
		} else {
			tokenString = security.ExtractToken(ctx.Request)
		}
		claims, err := security.ValidateToken(tokenString, config.Get().Token.SecretKey)
		if err != nil {
			fmt.Println(err.Error())
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"Error":   err.Error(),
				"Code":    http.StatusUnauthorized,
				"Message": "invalid token",
			})
			ctx.Abort()
			return
		}

		//rdsToken, err := serviceUser.GetToken(claims.Username)
		//if err != nil {
		//	rdsToken, err = serviceUser.GetToken("refresh_token:" + claims.Username)
		//	if err != nil {
		//		ctx.JSON(http.StatusUnauthorized, gin.H{
		//			"Error":   err.Error(),
		//			"Code":    http.StatusUnauthorized,
		//			"Message": "invalid token",
		//		})
		//		ctx.Abort()
		//		return
		//	}
		//}
		//if tokenString != rdsToken {
		//	ctx.JSON(http.StatusUnauthorized, gin.H{
		//		"Error":   err.Error(),
		//		"Code":    http.StatusUnauthorized,
		//		"Message": "invalid token",
		//	})
		//	ctx.Abort()
		//	return
		//}
		if claims.Username == "" {
			ctx.Set("username", claims.Email)
		} else {
			ctx.Set("email", claims.Username)
		}
		//user, err := serviceUser.GetByUsername(claims.Username)
		//if err != nil {
		//	ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
		//		"Error":   err.Error(),
		//		"Code":    http.StatusInternalServerError,
		//		"Message": "user not found",
		//	})
		//	return
		//}
		ctx.Set("user_id", claims.UserID)
		ctx.Set("role", claims.Role)
		if !isRoleAllowed(claims.Role, allowedRoles...) {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"Error":   "permission denied",
				"Code":    http.StatusForbidden,
				"Message": "permission denied",
			})
			ctx.Abort()
		}
		// Собираем тело запроса
		bodyBytes, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"Error":   err.Error(),
				"Code":    http.StatusInternalServerError,
				"Message": "error internal server",
			})
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		// Собираем параметры маршрута
		params := make(map[string]string)
		for _, param := range ctx.Params {
			params[param.Key] = param.Value
		}
		responseBody := new(bytes.Buffer)
		writer := &bodyWriter{body: responseBody, ResponseWriter: ctx.Writer}
		ctx.Writer = writer
		ctx.Next()
	}
}

func isRoleAllowed(role string, allowedRoles ...string) bool {
	allowedRolesMap := make(map[string]bool)
	for _, allowedRole := range allowedRoles {
		allowedRolesMap[allowedRole] = true
	}

	if allowedRolesMap["all"] {
		return true
	}
	if role == constants.SuperAdminRole {
		return true
	}

	if allowedRolesMap[role] {
		return true
	}

	return false
}
