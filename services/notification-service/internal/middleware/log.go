package middleware

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"notification-service/core/connect"
	"time"
)

// Log is a middleware that logs requests and responses.
func Log(ctx *gin.Context) {
	startTime := time.Now()

	// Continue to the next handler
	ctx.Next()

	latency := time.Since(startTime).Milliseconds()

	// Capture the response body from the CustomResponseWriter
	userIdI, userIdExist := ctx.Get("user_id")
	var userId uint
	if userIdExist {
		userId = userIdI.(uint)
	}
	connect.Logger.Info("INFO",
		zap.Uint("userId", userId),
		zap.String("method", ctx.Request.Method),
		zap.String("path", ctx.Request.RequestURI),
		//zap.String("request_body", string(requestBodyByte)),
		//zap.String("response_body", responseBody),
		zap.Int("status_code", ctx.Writer.Status()),
		zap.String("client_ip", ctx.ClientIP()),
		zap.Int64("response_time", latency),
	)
}
