package wshandlers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"log"
	"net/http"
	"notification-service/core/connect"
	"notification-service/internal/constants"
	"notification-service/internal/controllers"
	"notification-service/internal/dto"
	"notification-service/internal/middleware"
	"notification-service/internal/services"
)

type UserHandler struct {
	upgrade *websocket.Upgrader
	service services.UserServiceInterface
}

func NewUserHandler(upgrade *websocket.Upgrader, service services.UserServiceInterface) *UserHandler {
	return &UserHandler{
		upgrade: upgrade,
		service: service,
	}
}

func (uh *UserHandler) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	g.GET("connect", middleware.Authentication(constants.AllRole), controllers.AppHandler(uh.OperatorHandler).Handle)
	return g
}

func (uh *UserHandler) OperatorHandler(c *gin.Context) *controllers.AppResp {
	userId, _ := c.Get("user_id")
	role, _ := c.Get("role")
	conn, err := uh.upgrade.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Println("Error upgrading operator connection:", err)
		return &controllers.AppResp{
			Error:   err.Error(),
			Code:    http.StatusInternalServerError,
			Message: "failed to upgrade connection",
		}
	}
	defer conn.Close()
	if role == constants.AdminRole || role == constants.SuperAdminRole {
		connect.WsManager.AddAdminConnection(userId.(uint), conn)
	} else if role == constants.OwnerRole || role == constants.StaffRole {
		connect.WsManager.AddClientConnection(userId.(uint), conn)
	}
	defer func() {
		fmt.Println("delete operator connection from map")
		if role == constants.AdminRole || role == constants.SuperAdminRole {
			connect.WsManager.RemoveAdminConnection(userId.(uint))
		} else if role == constants.OwnerRole || role == constants.StaffRole {
			connect.WsManager.RemoveClientConnection(userId.(uint))
		}
		_ = conn.Close()
		fmt.Println("WebSocket connection closed.")
	}()

	//testData := map[string]interface{}{
	//	"test": "data",
	//}
	//if err = conn.WriteJSON(testData); err != nil {
	//	fmt.Println("failed to send test data")
	//	return &controllers.AppResp{
	//		Error:   err.Error(),
	//		Code:    http.StatusInternalServerError,
	//		Message: "failed to send data to connection",
	//	}
	//}
	// Continuously listen for incoming messages (although you won't need this for now)
	for {
		var p dto.NotificationPayload
		err = conn.ReadJSON(&p)
		fmt.Println(p.Type)
		if err != nil {
			return &controllers.AppResp{
				Error:   err.Error(),
				Code:    http.StatusInternalServerError,
				Message: "failed to read message",
			}
		}
	}
}
