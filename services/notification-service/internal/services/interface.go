package services

import (
	"notification-service/core/config"
	"notification-service/internal/repositories"
)

type Service struct {
	UserServiceInterface
	NotificationServiceInterface
}

func NewService(repos *repositories.Repository, conf config.Config) *Service {
	return &Service{
		UserServiceInterface:         NewUserService(repos.UserRepositoryInterface),
		NotificationServiceInterface: NewNotificationService(repos.NotificationRepositoryInterface),
	}
}
