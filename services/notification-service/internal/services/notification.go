package services

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"notification-service/internal/dto"
	"notification-service/internal/models"
	"notification-service/internal/repositories"
)

type NotificationServiceInterface interface {
	Create(notification *models.Notification) error
	UpdateStatus(id primitive.ObjectID, userId uint, isDelivered bool) (*models.Notification, error)
	GetAllByUserId(userId uint, query *dto.NotificationQuery, limit, offset int64) (int64, []models.Notification, error)
}

type NotificationService struct {
	repo repositories.NotificationRepositoryInterface
}

func NewNotificationService(repo repositories.NotificationRepositoryInterface) *NotificationService {
	return &NotificationService{
		repo: repo,
	}
}

func (us *NotificationService) Create(notification *models.Notification) error {
	return us.repo.Create(notification)
}

func (us *NotificationService) UpdateStatus(id primitive.ObjectID, userId uint, isDelivered bool) (*models.Notification, error) {
	return us.repo.UpdateStatus(id, userId, isDelivered)
}

func (us *NotificationService) GetAllByUserId(userId uint, query *dto.NotificationQuery, limit, offset int64) (int64, []models.Notification, error) {
	return us.repo.GetAllByUserId(userId, query, limit, offset)
}
