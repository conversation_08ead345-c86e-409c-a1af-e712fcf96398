package repositories

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"notification-service/internal/constants"
	"notification-service/internal/dto"
	"notification-service/internal/models"
	"time"
)

type NotificationRepositoryInterface interface {
	Create(notification *models.Notification) error
	UpdateStatus(id primitive.ObjectID, userId uint, isDelivered bool) (*models.Notification, error)
	GetAllByUserId(userId uint, query *dto.NotificationQuery, limit, offset int64) (int64, []models.Notification, error)
	CreateTransaction(transaction *models.Transaction) error
}

type NotificationRepository struct {
	mongodb *mongo.Client
}

func NewNotificationRepository(mongodb *mongo.Client) *NotificationRepository {
	return &NotificationRepository{
		mongodb: mongodb,
	}
}

func (nr *NotificationRepository) collection() *mongo.Collection {
	return nr.mongodb.Database("monexa_db").Collection("notifications")
}

func (nr *NotificationRepository) Create(notification *models.Notification) error {
	notification.ID = primitive.NewObjectID()
	notification.CreatedAt = time.Now()

	if notification.Transaction != nil {
		notification.Transaction.ID = primitive.NewObjectID()
		notification.Transaction.NotificationID = notification.ID
	}

	if _, err := nr.collection().InsertOne(context.TODO(), notification); err != nil {
		return err
	}
	return nil
}

func (nr *NotificationRepository) CreateTransaction(transaction *models.Transaction) error {
	transaction.ID = primitive.NewObjectID()
	if _, err := nr.mongodb.Database("monexa_db").Collection("transactions").InsertOne(context.TODO(), transaction); err != nil {
		return err
	}
	return nil
}

func (nr *NotificationRepository) UpdateStatus(id primitive.ObjectID, userId uint, isDelivered bool) (*models.Notification, error) {
	filter := bson.M{"_id": id, "user_id": userId}
	update := bson.M{"$set": bson.M{"is_delivered": isDelivered}}
	if _, err := nr.collection().UpdateOne(context.TODO(), filter, update); err != nil {
		return nil, err
	}
	var notification models.Notification
	if err := nr.collection().FindOne(context.TODO(), filter).Decode(&notification); err != nil {
		return nil, err
	}

	return &notification, nil
}

func (nr *NotificationRepository) GetAllByUserId(userId uint, query *dto.NotificationQuery, limit, offset int64) (int64, []models.Notification, error) {
	filter := bson.M{"user_id": userId}
	if query.Status == constants.NotificationStatusRead {
		filter["is_delivered"] = true
	} else if query.Status == constants.NotificationStatusNotRead {
		filter["is_delivered"] = false
	}
	total, err := nr.collection().CountDocuments(context.TODO(), filter)
	if err != nil {
		return 0, nil, err
	}

	findOptions := options.Find().SetLimit(limit).SetSkip(offset).SetSort(bson.D{{"created_at", -1}})

	cursor, err := nr.collection().Find(context.TODO(), filter, findOptions)
	if err != nil {
		return 0, nil, err
	}
	defer cursor.Close(context.TODO())
	var notifications []models.Notification
	for cursor.Next(context.TODO()) {
		var n models.Notification
		if err = cursor.Decode(&n); err != nil {
			return 0, nil, err
		}
		notifications = append(notifications, n)
	}
	return total, notifications, nil
}
