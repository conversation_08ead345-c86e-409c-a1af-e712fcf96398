package repositories

import (
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	UserRepositoryInterface
	NotificationRepositoryInterface
}

func NewRepository(mongoDb *mongo.Client, rd *redis.Client) *Repository {
	return &Repository{
		UserRepositoryInterface:         NewUserRepository(rd),
		NotificationRepositoryInterface: NewNotificationRepository(mongoDb),
	}
}
