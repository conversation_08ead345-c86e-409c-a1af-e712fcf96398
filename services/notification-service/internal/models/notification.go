package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type Notification struct {
	ID          primitive.ObjectID `bson:"_id,omitempty"`
	UserID      uint               `bson:"user_id"`
	Role        string
	Message     string       `bson:"message"`
	Type        string       `bson:"type"`
	IsDelivered bool         `bson:"is_delivered"`
	CreatedAt   time.Time    `bson:"created_at"`
	Transaction *Transaction `bson:"transaction,omitempty"`
}

//todo create transaction model

type Transaction struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	NotificationID primitive.ObjectID `bson:"notification_id"` // Reference to Notification
	AccountName    string             `bson:"account_name"`
	Amount         string             `bson:"amount"`
	OperationType  string             `bson:"operation_type"`
	Direction      string             `bson:"direction"`
	Currency       string             `bson:"currency"`
}
