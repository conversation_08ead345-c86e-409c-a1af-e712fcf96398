package controllers

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"net/http"
	"notification-service/internal/constants"
	"notification-service/internal/dto"
	"notification-service/internal/middleware"
	"notification-service/internal/services"
)

type NotificationController struct {
	service     services.NotificationServiceInterface
	userService services.UserServiceInterface
}

func NewNotificationController(service services.NotificationServiceInterface, userService services.UserServiceInterface) *NotificationController {
	return &NotificationController{
		service:     service,
		userService: userService,
	}
}

func (nc *NotificationController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	g.GET("get-all", middleware.Authentication(constants.AllRole), AppHandler(nc.GetAllByUserId).Handle)
	g.PATCH("read/:notification_id", middleware.Authentication(constants.AllRole), AppHandler(nc.Read).Handle)
	return g
}

func (nc *NotificationController) GetAllByUserId(ctx *gin.Context) *AppResp {
	userId, exists := ctx.Get("user_id")
	if !exists {
		return &AppResp{
			Error:   "failed to get user id",
			Code:    http.StatusInternalServerError,
			Message: "internal server error",
		}
	}

	var pag dto.PaginationDto
	if err := ctx.ShouldBindQuery(&pag); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Code:    http.StatusBadRequest,
			Message: "bad request",
		}
	}
	pag.Default()

	var query dto.NotificationQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	count, notifications, err := nc.service.GetAllByUserId(userId.(uint), &query, pag.Limit, (pag.Page-1)*pag.Limit)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Code:    http.StatusInternalServerError,
			Message: "internal server error",
		}
	}

	var resp = dto.AllNotificationsDtoResp{
		Count:         count,
		Notifications: notifications,
	}
	return Ok(ctx, resp)
}

func (nc *NotificationController) Read(ctx *gin.Context) *AppResp {
	userId, exists := ctx.Get("user_id")
	if !exists {
		return &AppResp{
			Error:   "failed to get user id",
			Code:    http.StatusInternalServerError,
			Message: "internal server error",
		}
	}

	id := ctx.Param("notification_id")
	notificationId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Code:    http.StatusInternalServerError,
			Message: "internal server error",
		}
	}
	_, err = nc.service.UpdateStatus(notificationId, userId.(uint), true)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Code:    http.StatusInternalServerError,
			Message: "internal server error",
		}
	}
	return Ok(ctx, "ok")
}
