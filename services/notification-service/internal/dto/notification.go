package dto

import (
	"notification-service/internal/models"
	"time"
)

type NotificationPayload struct {
	ID          string
	UserId      uint
	Type        string
	Message     string
	Role        string
	Transaction *NotificationTransactionPayload
	CreatedAt   time.Time
}

type NotificationTransactionPayload struct {
	AccountName   string
	Amount        string
	OperationType string
	Direction     string
	Currency      string
}

type AllNotificationsDtoResp struct {
	Count         int64
	Notifications []models.Notification
}

type NotificationQuery struct {
	Status string
}
