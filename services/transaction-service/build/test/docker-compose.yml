version: '3.7'

services:
  transaction_service:
    container_name: transaction-service
    build:
      context: ../../
      dockerfile: build/test/Dockerfile
    restart: always
    ports:
      - "8081:8081"
    volumes:
      - ../../logs:/app/logs

    depends_on:
      - transaction_postgres_db


    command: ["./api"]
    networks:
      - test_infrastructure_network

  transaction_postgres_db:
    image: postgres:latest
    container_name: transaction-postgres-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=fafQWllRTl4nfs
      - POSTGRES_DB=monexa_db
    ports:
      - "5433:5433"
    volumes:
      - monexa_transaction_postgres_data:/var/lib/postgresql/data
    networks:
      - test_infrastructure_network
    command: -p 5433

#  auth_seed:
#    container_name: auth-seed
#    build:
#      context: ../../
#      dockerfile: build/test/Dockerfile
#    restart: on-failure
#    depends_on:
#      - auth_postgres_db
#      - auth_redis
#    command: ["./seeder"]
#    networks:
#      - test_infrastructure_network


#  promtail:
#    image: grafana/promtail:2.7.1
#    container_name: promtail
#    volumes:
#      - ../../logs:/var/log
#      - ./promtail-config.yaml:/etc/promtail/promtail.yaml
#    command: -config.file=/etc/promtail/promtail.yaml
#    networks:
#      - test_infrastructure_network


volumes:
  monexa_transaction_postgres_data:
    driver: local

networks:
  test_infrastructure_network:
    external: true

