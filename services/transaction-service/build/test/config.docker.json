{"server": {"host": "0.0.0.0", "port": 8081}, "database": {"host": "transaction-postgres-db", "port": 5433, "name": "monexa_db", "username": "postgres", "password": "fafQWllRTl4nfs", "sslMode": "disable"}, "rabbitMQ": {"host": "monexa-rabbitmq", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "authService": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRyYW5zYWN0aW9uIiwidXNlcl9pZCI6NCwicGVybWlzc2lvbnMiOm51bGwsIklzc3VlZEF0IjoiMjAyNS0wNi0xM1QwNjowMTowNC4zMTQ1OTcwNTZaIiwicm9sZSI6InRyYW5zYWN0aW9uIn0.QEsQbAWqrnnQyOWWfdmP645oDH15rd9h_ueZXLQCl-U", "host": "http://**************:8091/"}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "token": {"secretKey": "aafsdfwe23rfasdvrg9fvjwfwu4832k2masfgjouergrg"}, "redis": {"address": "redis:6379", "db": 0, "password": "dfff34#2pl#ghl"}, "templatePath": {"rootPath": "./templates", "transactionReceipt": "transaction_cheque.html"}}