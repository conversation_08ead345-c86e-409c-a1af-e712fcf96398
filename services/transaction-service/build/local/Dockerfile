# Используем легковесный базовый образ для финальной стадии
FROM golang:latest as builder
ENV GO111MODULE=on
WORKDIR /build

# Копируем и скачиваем зависимости, чтобы использовать кэш
COPY go.mod go.sum ./
RUN go mod download

# Копируем исходный код и выполняем сборку
COPY . .
RUN make -B all

# Используем легковесный образ для финального контейнера
FROM alpine:latest
WORKDIR /app


# Устанавливаем необходимые пакеты
RUN apk --no-cache add bash
RUN apt-get -y install wkhtmltopdf

# Создаем директорию и копируем файлы из билдера
COPY --from=builder /build/bin/api ./
COPY build/local/config.docker.json ./config.json

# Устанавливаем команду по умолчанию (если необходимо)
CMD ["./api", "./seeder"]