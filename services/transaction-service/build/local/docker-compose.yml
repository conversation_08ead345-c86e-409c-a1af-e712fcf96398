version: '3.7'

services:
  transaction_service:
    container_name: transaction-service
    build:
      context: ../../
      dockerfile: build/local/Dockerfile
    restart: always
    ports:
      - "9091:9091"
    volumes:
      - ../../logs:/app/logs

    depends_on:
      - transaction_postgres_db

    command: ["./api"]
    networks:
      - infrastructure_network

  transaction_postgres_db:
    image: postgres:latest
    container_name: transaction-postgres-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=fafQWllRTl4nfs
      - POSTGRES_DB=monexa_db
    ports:
      - "5434:5434"
    volumes:
      - transaction_postgres_data:/var/lib/postgresql/data
    networks:
      - infrastructure_network
    command: -p 5434

#  transaction_redis:
#    image: redis
#    container_name: transaction-redis
#    environment:
#      - REDIS_PASSWORD=dfff34#2pl#ghl
#    restart: always
#    ports:
#      - "6381:6379"
#    volumes:
#      - monexa_transaction_redis_data:/data
#    networks:
#      - local_infrastructure_network


  transaction_promtail:
    image: grafana/promtail:2.7.1
    container_name: transaction-promtail
    volumes:
      - ../../logs:/var/log
      - ./promtail-config.yaml:/etc/promtail/promtail.yaml
    command: -config.file=/etc/promtail/promtail.yaml
    networks:
      - infrastructure_network


volumes:
  transaction_postgres_data:
    driver: local
#  monexa_transaction_redis_data:
#    driver: local

networks:
  infrastructure_network:
    external: true

