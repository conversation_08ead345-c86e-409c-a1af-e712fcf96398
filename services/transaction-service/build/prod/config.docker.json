{"server": {"host": "0.0.0.0", "port": 8091}, "database": {"host": "auth-postgres-db", "port": 5434, "name": "monexa_db", "username": "postgres", "password": "fafQWllRTl4nfs", "sslMode": "disable"}, "rabbitMQ": {"host": "monexa-rabbitmq", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "authService": {"token": "", "host": ""}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "token": {"secretKey": "project_secret_key"}, "redis": {"address": "auth-redis:6379", "db": 0, "password": "dfff34#2pl#ghl"}, "templatePath": {"rootPath": "./templates", "transactionReceipt": "transaction_cheque.html"}}