<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Transaction Cheque</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .cheque {
            border: 1px solid #333;
            padding: 20px;
            max-width: 600px;
            margin: auto;
        }
        .section {
            margin-bottom: 15px;
        }
        .section h3 {
            margin-bottom: 5px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 3px;
        }
        .label {
            font-weight: bold;
        }
        .value {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="cheque">
        <h2>Transaction Cheque</h2>

        <div class="section">
            <span class="label">Transaction ID:</span> <span class="value">{{.TransactionId}}</span><br>
            <span class="label">Slip ID:</span> <span class="value">{{.SlipId}}</span><br>
            <span class="label">Type:</span> <span class="value">{{.Type}}</span><br>
            <span class="label">Direction:</span> <span class="value">{{.Direction}}</span><br>
            <span class="label">Status:</span> <span class="value">{{.Status}}</span><br>
            <span class="label">Request Date:</span> <span class="value">{{.RequestDate.Format "2006-01-02 15:04:05"}}</span>
        </div>

        <div class="section">
            <h3>Account Info</h3>
            <span class="label">Account ID:</span> <span class="value">{{.AccountId}}</span><br>
            <span class="label">Account Name:</span> <span class="value">{{.Account.Name}}</span><br>
            <span class="label">User Role:</span> <span class="value">{{.UserRole}}</span>
        </div>

        <div class="section">
            <h3>Amounts</h3>
            <span class="label">Processing Amount:</span>
            <span class="value">{{.ProcessingAmount}} {{.ProcessingAmountCurrency}}</span><br>

            <span class="label">Merchant Amount:</span>
            <span class="value">{{.MerchantAmount}} {{.MerchantAmountCurrency}}</span>
        </div>

        <div class="section">
            <h3>Sender & Recipient</h3>
            {{if .Sender}}
                <span class="label">Sender ID:</span> <span class="value">{{.SenderId}}</span><br>
                <span class="label">Sender Name:</span> <span class="value">{{.Sender.Name}}</span><br>
            {{end}}

            {{if .Recipient}}
                <span class="label">Recipient ID:</span> <span class="value">{{.RecipientId}}</span><br>
                <span class="label">Recipient Name:</span> <span class="value">{{.Recipient.Name}}</span><br>
                <span class="label">Recipient Reference:</span> <span class="value">{{.RecipientReference}}</span>
            {{end}}
        </div>

        <div class="section">
            <h3>Fee</h3>
            <span class="label">Fee ID:</span> <span class="value">{{.FeeId}}</span><br>
            <span class="label">Fee Description:</span> <span class="value">{{.Fee.OperationType}}</span><br>
            <span class="label">Fee Amount:</span> <span class="value">{{.Fee.FixedAmount}}</span>
        </div>
    </div>
</body>
</html>
