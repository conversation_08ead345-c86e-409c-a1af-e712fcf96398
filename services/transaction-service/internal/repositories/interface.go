package repositories

import (
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type Repository struct {
	IbanRepositoryInterface
	BankRepositoryInterface
	CurrencyRepositoryInterface
	UserRepositoryInterface
	FeeRepositoryInterface
	AccountRepositoryInterface
	TransactionRepositoryInterface
}

func NewRepository(db *gorm.DB, rds *redis.Client) *Repository {
	return &Repository{
		IbanRepositoryInterface:        NewIbanRepository(db),
		BankRepositoryInterface:        NewBankRepository(rds, db),
		CurrencyRepositoryInterface:    NewCurrencyRepository(rds, db),
		UserRepositoryInterface:        NewUserRepository(rds),
		FeeRepositoryInterface:         NewFeeRepository(rds, db),
		AccountRepositoryInterface:     NewAccountRepository(rds, db),
		TransactionRepositoryInterface: NewTransactionRepository(db),
	}
}
