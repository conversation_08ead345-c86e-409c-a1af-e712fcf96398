package repositories

import (
	"context"
	"encoding/json"
	"time"
	"transaction-service/internal/dto"
	"transaction-service/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CurrencyRepositoryInterface interface {
	CreateCurrency(currency *models.Currency) error
	GetAll() ([]models.Currency, error)
	GetOne(id uint) (*models.Currency, error)
	Update(currency *models.Currency) error
	Delete(id uint) error

	CreateCurrencyExchange(currency *models.CurrencyExchange) error
	UpdateCurrencyExchange(currencyExchange *models.CurrencyExchange) error
	DeleteCurrencyExchange(id uint) error
	GetOneCurrencyExchange(id uint) (*models.CurrencyExchange, error)
	GetAllCurrencyExchange(query *dto.CurrencyExchangeGetAllQuery) ([]models.CurrencyExchange, error)
	GetCurrencyExchangeByIds(firstCurrencyId uint, secondCurrencyId uint) (*models.CurrencyExchange, error)
	GetCurrencyExchangeBidirectional(firstCurrencyId uint, secondCurrencyId uint) (*models.CurrencyExchange, error)
}

type CurrencyRepository struct {
	rds *redis.Client
	db  *gorm.DB
}

func NewCurrencyRepository(rds *redis.Client, db *gorm.DB) *CurrencyRepository {
	return &CurrencyRepository{
		rds: rds,
		db:  db,
	}
}

func (cr *CurrencyRepository) CreateCurrencyExchange(currency *models.CurrencyExchange) error {
	return cr.db.Create(currency).Error
}

func (cr *CurrencyRepository) UpdateCurrencyExchange(currencyExchange *models.CurrencyExchange) error {
	return cr.db.Save(currencyExchange).Error
}

func (cr *CurrencyRepository) DeleteCurrencyExchange(id uint) error {
	return cr.db.Delete(&models.CurrencyExchange{}, id).Error
}

func (cr *CurrencyRepository) GetOneCurrencyExchange(id uint) (*models.CurrencyExchange, error) {
	var currencyExchange models.CurrencyExchange
	if err := cr.db.Preload(clause.Associations).First(&currencyExchange, id).Error; err != nil {
		return nil, err
	}
	return &currencyExchange, nil
}

func (cr *CurrencyRepository) GetCurrencyExchangeBidirectional(firstCurrencyId uint, secondCurrencyId uint) (*models.CurrencyExchange, error) {
	ce, err := cr.GetCurrencyExchangeByIds(firstCurrencyId, secondCurrencyId)
	if err != nil {
		return ce, err
	}
	if ce.ID == 0 {
		ce, err = cr.GetCurrencyExchangeByIds(secondCurrencyId, firstCurrencyId)
	}
	return ce, err

}

func (cr *CurrencyRepository) GetCurrencyExchangeByIds(firstCurrencyId uint, secondCurrencyId uint) (*models.CurrencyExchange, error) {
	var currencyExchange models.CurrencyExchange
	if err := cr.db.Where("first_currency_id = ? AND second_currency_id = ?", firstCurrencyId, secondCurrencyId).Find(&currencyExchange).Error; err != nil {
		return nil, err
	}
	return &currencyExchange, nil
}

func (cr *CurrencyRepository) GetAllCurrencyExchange(query *dto.CurrencyExchangeGetAllQuery) ([]models.CurrencyExchange, error) {
	var queryModel = cr.db.Model(&models.CurrencyExchange{})
	if query.CurrencyId != 0 {
		queryModel = queryModel.Where("first_currency_id = ? OR second_currency_id = ?", query.CurrencyId, query.CurrencyId)
	}
	var currencyExchanges []models.CurrencyExchange
	if err := queryModel.Preload(clause.Associations).Find(&currencyExchanges).Error; err != nil {
		return nil, err
	}

	return currencyExchanges, nil
}
func (cr *CurrencyRepository) CreateCurrency(currency *models.Currency) error {
	err := cr.db.Create(currency).Error
	if err == nil {
		cr.rds.Del(context.Background(), "currencies")
	}
	return err
}

func (cr *CurrencyRepository) GetAll() ([]models.Currency, error) {
	var currencies []models.Currency

	val, err := cr.rds.Get(context.Background(), "currencies").Result()
	if err == nil {
		if err = json.Unmarshal([]byte(val), &currencies); err == nil {
			return currencies, nil
		}
	}

	if err = cr.db.Find(&currencies).Error; err != nil {
		return nil, err
	}

	_ = cr.rds.Set(context.Background(), "currencies", currencies, time.Hour*24)

	return currencies, err
}

func (cr *CurrencyRepository) GetOne(id uint) (*models.Currency, error) {
	var currency models.Currency

	if err := cr.db.First(&currency, id).Error; err != nil {
		return nil, err
	}

	return &currency, nil
}

func (cr *CurrencyRepository) Update(currency *models.Currency) error {
	err := cr.db.Save(currency).Error
	if err == nil {
		cr.rds.Del(context.Background(), "currencies")
	}
	return err
}

func (cr *CurrencyRepository) Delete(id uint) error {
	err := cr.db.Delete(&models.Currency{}, id).Error
	if err == nil {
		cr.rds.Del(context.Background(), "currencies")
	}
	return err
}
