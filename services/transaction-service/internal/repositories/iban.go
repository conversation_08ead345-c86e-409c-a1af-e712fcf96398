package repositories

import (
	"gorm.io/gorm"
	"transaction-service/internal/models"
	"transaction-service/utils"
)

type IbanRepositoryInterface interface {
	CreateIban(iban *models.Iban) error
	GetAll(pag *utils.Pagination) ([]models.Iban, int64, error)
	GetOne(id uint) (*models.Iban, error)
	Update(iban *models.Iban) error
	Delete(id uint) error
}

type IbanRepository struct {
	db *gorm.DB
}

func NewIbanRepository(db *gorm.DB) *IbanRepository {
	return &IbanRepository{
		db: db,
	}
}

func (ir *IbanRepository) CreateIban(iban *models.Iban) error {
	err := ir.db.Create(iban).Error
	return err
}

func (ir *IbanRepository) GetAll(pag *utils.Pagination) ([]models.Iban, int64, error) {
	var ibans []models.Iban
	var total int64

	if err := ir.db.Model(&models.Iban{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (pag.Page - 1) * pag.Limit

	if err := ir.db.
		Preload("Account").
		Preload("Currency").
		Offset(offset).
		Limit(pag.Limit).
		Find(&ibans).Error; err != nil {
		return nil, 0, err
	}

	return ibans, total, nil
}

func (ir *IbanRepository) GetOne(id uint) (*models.Iban, error) {
	var iban models.Iban

	if err := ir.db.
		Preload("Account").Preload("Currency").
		Find(&iban, id).Error; err != nil {
		return nil, err
	}
	return &iban, nil
}

func (ir *IbanRepository) Update(iban *models.Iban) error {
	err := ir.db.Model(&models.Iban{}).
		Where("id = ?", iban.ID).
		Updates(map[string]interface{}{
			"account_id":  iban.AccountId,
			"iban":        iban.IBAN,
			"description": iban.Description,
			"currency_id": iban.CurrencyId,
		}).Error
	return err
}

func (ir *IbanRepository) Delete(id uint) error {
	err := ir.db.Delete(&models.Iban{}, id).Error

	return err
}
