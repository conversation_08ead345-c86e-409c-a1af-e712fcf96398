package repositories

import (
	"context"
	"github.com/redis/go-redis/v9"
)

type UserRepositoryInterface interface {
	GetToken(username string) (string, error)
}

var ctx = context.Background()

type UserRepository struct {
	rds *redis.Client
}

func NewUserRepository(rds *redis.Client) *UserRepository {
	return &UserRepository{
		rds: rds,
	}
}

func (ur *UserRepository) GetToken(username string) (string, error) {
	return ur.rds.Get(ctx, username).Result()
}

