package repositories

import (
	"context"
	"encoding/json"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"time"
	"transaction-service/internal/models"
)

type FeeRepositoryInterface interface {
	GetAllFeeCategories() ([]models.FeeCategory, error)
	UpdateFeeCategory(category *models.FeeCategory) error
	GetAllFeeTypes() ([]models.FeeType, error)
	CreateFeeCategory(category *models.FeeCategory) error
	DeleteFeeCategory(id uint) error
	CreateFeeType(ft *models.FeeType) error
	UpdateFeeType(ft *models.FeeType) error
	DeleteFeeType(id uint) error
	CreateFee(fee *models.Fee) error
	UpdateFee(fee *models.Fee) error
	GetFeesByAccountID(accountID uint) ([]models.Fee, error)
	DeleteFee(id uint) error
	GetFeeCategoryById(id uint) (*models.FeeCategory, error)
	GetFeeTypeById(id uint) (*models.FeeType, error)
	GetFeeById(id uint) (*models.Fee, error)
}

type FeeRepository struct {
	rds *redis.Client
	db  *gorm.DB
}

func NewFeeRepository(rds *redis.Client, db *gorm.DB) *FeeRepository {
	return &FeeRepository{
		rds: rds,
		db:  db,
	}
}

func (r *FeeRepository) GetFeeCategoryById(id uint) (*models.FeeCategory, error) {
	var feeCategory models.FeeCategory
	if err := r.db.First(&feeCategory, id).Error; err != nil {
		return nil, err
	}
	return &feeCategory, nil
}

func (r *FeeRepository) GetFeeTypeById(id uint) (*models.FeeType, error) {
	var feeType models.FeeType
	if err := r.db.First(&feeType, id).Error; err != nil {
		return nil, err
	}
	return &feeType, nil
}

func (r *FeeRepository) GetFeeById(id uint) (*models.Fee, error) {
	var fee models.Fee
	if err := r.db.First(&fee, id).Error; err != nil {
		return nil, err
	}
	return &fee, nil
}

func (r *FeeRepository) GetAllFeeCategories() ([]models.FeeCategory, error) {
	var categories []models.FeeCategory
	val, err := r.rds.Get(context.Background(), "fee_categories").Result()
	if err == nil {
		if err = json.Unmarshal([]byte(val), &categories); err != nil {
			return categories, nil
		}
	}
	if err = r.db.Preload("FeeTypes").Find(&categories).Error; err != nil {
		return nil, err
	}
	data, _ := json.Marshal(categories)
	r.rds.Set(context.Background(), "fee_categories", data, time.Hour*24)
	return categories, nil
}

func (r *FeeRepository) CreateFeeCategory(category *models.FeeCategory) error {
	err := r.db.Create(category).Error
	if err == nil {
		r.rds.Del(context.Background(), "fee_categories")
	}
	return err
}

func (r *FeeRepository) UpdateFeeCategory(category *models.FeeCategory) error {
	err := r.db.Save(category).Error
	if err == nil {
		r.rds.Del(context.Background(), "fee_categories")
	}
	return err
}

func (r *FeeRepository) DeleteFeeCategory(id uint) error {
	err := r.db.Unscoped().Delete(&models.FeeCategory{}, id).Error
	if err == nil {
		r.rds.Del(context.Background(), "fee_categories")
	}
	return err
}

func (r *FeeRepository) GetAllFeeTypes() ([]models.FeeType, error) {
	var types []models.FeeType
	var res, err = r.rds.Get(context.Background(), "fee_types").Result()
	if err == nil {
		if err = json.Unmarshal([]byte(res), &types); err == nil {
			return types, nil
		}
	}
	if err = r.db.Find(&types).Error; err != nil {
		return nil, err
	}
	data, _ := json.Marshal(types)
	r.rds.Set(context.Background(), "fee_types", data, time.Hour*24)
	return types, nil
}

func (r *FeeRepository) CreateFeeType(ft *models.FeeType) error {
	err := r.db.Create(ft).Error
	if err == nil {
		r.rds.Del(context.Background(), "fee_types")
	}
	return err
}

func (r *FeeRepository) UpdateFeeType(ft *models.FeeType) error {
	err := r.db.Save(ft).Error
	if err == nil {
		r.rds.Del(context.Background(), "fee_types")
	}
	return err
}

func (r *FeeRepository) DeleteFeeType(id uint) error {
	err := r.db.Delete(&models.FeeType{}, id).Error
	if err == nil {
		r.rds.Del(context.Background(), "fee_types")
	}
	return err
}

func (r *FeeRepository) CreateFee(fee *models.Fee) error {
	return r.db.Create(fee).Error
}

func (r *FeeRepository) UpdateFee(fee *models.Fee) error {
	return r.db.Save(fee).Error
}

func (r *FeeRepository) GetFeesByAccountID(accountID uint) ([]models.Fee, error) {
	var fees []models.Fee
	err := r.db.Where("account_id = ?", accountID).Find(&fees).Error
	return fees, err
}

func (r *FeeRepository) DeleteFee(id uint) error {
	return r.db.Delete(&models.Fee{}, id).Error
}
