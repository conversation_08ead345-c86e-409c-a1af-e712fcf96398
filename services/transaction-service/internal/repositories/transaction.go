package repositories

import (
	"transaction-service/internal/dto"
	"transaction-service/internal/models"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TransactionRepositoryInterface interface {
	GetOneTransaction(id uint) (*models.Transaction, error)
	SaveTransaction(tx *gorm.DB, transaction *models.Transaction) error
	WithTransaction(fc func(tx *gorm.DB) error) error
	GetAllTransaction(query *dto.TransactionQueryDto, pag *dto.PaginationDto) (*dto.GetAllTransactionResp, error)
	UpdateTransactionStatus(id uint, status string) error
	GetRecipientsByOwnerId(ownerId uint) ([]models.Recipient, error)
	GetRecipientById(id uint) (*models.Recipient, error)
}

type TransactionRepository struct {
	db *gorm.DB
}

func NewTransactionRepository(db *gorm.DB) *TransactionRepository {
	return &TransactionRepository{
		db: db,
	}
}

func (tr *TransactionRepository) GetOneTransaction(id uint) (*models.Transaction, error) {
	var transaction models.Transaction
	if err := tr.db.Preload(clause.Associations).First(&transaction, id).Error; err != nil {
		return nil, err
	}
	return &transaction, nil

}

func (tr *TransactionRepository) SaveTransaction(tx *gorm.DB, transaction *models.Transaction) error {
	return tx.Create(transaction).Error
}

func (tr *TransactionRepository) WithTransaction(fc func(tx *gorm.DB) error) error {
	return tr.db.Transaction(fc)
}

func (tr *TransactionRepository) GetAllTransaction(query *dto.TransactionQueryDto, pag *dto.PaginationDto) (*dto.GetAllTransactionResp, error) {
	var transactions []models.Transaction
	var queryModel = tr.db.Model(&models.Transaction{})
	if query.AccountId != 0 {
		queryModel = queryModel.Where("account_id = ?", query.AccountId)
	}
	if query.Type != "" {
		queryModel = queryModel.Where("type = ?", query.Type)
	}
	if query.Status != "" {
		queryModel = queryModel.Where("status = ?", query.Status)
	}
	if query.Direction != "" {
		queryModel = queryModel.Where("direction = ?", query.Direction)
	}

	var count int64
	if err := queryModel.Count(&count).Error; err != nil {
		return nil, err
	}

	if err := queryModel.Offset((pag.Page - 1) * pag.Limit).Limit(pag.Limit).Order("created_at DESC").Preload(clause.Associations).Find(&transactions).Error; err != nil {
		return nil, err
	}
	var resp = dto.GetAllTransactionResp{
		Count: count,
		Data:  transactions,
	}
	return &resp, nil
}

func (tr *TransactionRepository) UpdateTransactionStatus(id uint, status string) error {
	return tr.db.Model(&models.Transaction{}).Where("id = ?", id).Update("status", status).Error
}

func (tr *TransactionRepository) GetRecipientsByOwnerId(ownerId uint) ([]models.Recipient, error) {
	var recipients []models.Recipient
	if err := tr.db.Where("owner_id = ?", ownerId).Find(&recipients).Error; err != nil {
		return nil, err
	}
	return recipients, nil
}

func (tr *TransactionRepository) GetRecipientById(id uint) (*models.Recipient, error) {
	var recipient models.Recipient
	if err := tr.db.First(&recipient, id).Error; err != nil {
		return nil, err
	}
	return &recipient, nil
}
