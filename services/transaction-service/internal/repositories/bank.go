package repositories

import (
	"context"
	"encoding/json"
	"time"
	"transaction-service/internal/models"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type BankRepositoryInterface interface {
	CreateBank(bank *models.Bank) error
	GetAll() ([]models.Bank, error)
	GetOne(id uint) (*models.Bank, error)
	Update(bank *models.Bank) error
	Delete(id uint) error
}

type BankRepository struct {
	rds *redis.Client
	db  *gorm.DB
}

func NewBankRepository(rds *redis.Client, db *gorm.DB) *BankRepository {
	return &BankRepository{
		rds: rds,
		db:  db,
	}
}

func (br *BankRepository) CreateBank(bank *models.Bank) error {
	err := br.db.Create(bank).Error
	if err == nil {
		br.rds.Del(context.Background(), "banks")
	}
	return err
}

func (br *BankRepository) GetAll() ([]models.Bank, error) {
	var banks []models.Bank

	val, err := br.rds.Get(context.Background(), "banks").Result()
	if err == nil {
		if err = json.Unmarshal([]byte(val), &banks); err == nil {
			return banks, nil
		}
	}

	if err = br.db.Find(&banks).Error; err != nil {
		return nil, err
	}

	_ = br.rds.Set(context.Background(), "banks", banks, time.Hour*24)

	return banks, nil
}

func (br *BankRepository) GetOne(id uint) (*models.Bank, error) {
	var bank models.Bank

	if err := br.db.First(&bank, id).Error; err != nil {
		return nil, err
	}

	return &bank, nil
}

func (br *BankRepository) Update(bank *models.Bank) error {
	err := br.db.Save(bank).Error
	if err == nil {
		br.rds.Del(context.Background(), "banks")
	}
	return err
}

func (br *BankRepository) Delete(id uint) error {
	err := br.db.Delete(&models.Bank{}, id).Error
	if err == nil {
		br.rds.Del(context.Background(), "banks")
	}
	return err
}
