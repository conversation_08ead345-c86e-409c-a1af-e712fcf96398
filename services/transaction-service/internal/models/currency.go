package models

import "github.com/shopspring/decimal"

type Currency struct {
	Entity
	CurrencyName string //USD
}

type CurrencyExchange struct {
	Entity
	FirstCurrencyId    uint            //USD
	FirstCurrency      Currency        `gorm:"ForeignKey:FirstCurrencyId"`
	SecondCurrencyId   uint            //SOM
	SecondCurrency     Currency        `gorm:"ForeignKey:SecondCurrencyId"`
	FirstCurrencySell  decimal.Decimal //87.73
	SecondCurrencySell decimal.Decimal //0.0114
}
