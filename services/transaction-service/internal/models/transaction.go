package models

import (
	"github.com/shopspring/decimal"
	"time"
)

type Transaction struct {
	Entity
	TransactionId            string //unique id
	SlipId                   string
	Type                     string //(withdrawal, deposit, fee)
	Direction                string //(out, in)
	Status                   string
	RequestDate              time.Time
	UserId                   uint
	UserRole                 string
	AccountId                uint // account A (usd)
	Account                  Account
	FeeId                    uint
	Fee                      Fee `gorm:"omitempty"`
	ProcessingAmount         decimal.Decimal
	ProcessingAmountCurrency string
	MerchantAmount           decimal.Decimal
	MerchantAmountCurrency   string
	RecipientId              *uint
	Recipient                *Recipient `gorm:"omitempty"`
	RecipientReference       string
	SenderId                 *uint
	Sender                   *Sender `gorm:"omitempty"`
}

type Recipient struct {
	Entity
	//TODO fields may change
	OwnerId       uint
	AccountNumber string
	BankCountry   string
	RecipientName string
	Description   string
	BicSwift      string
	CompanyName   string
	Email         string
}

type Sender struct {
	Entity
	Provider         string
	MerchantName     string
	MerchantId       string
	MerchantEmail    string
	ProcessingAmount decimal.Decimal
	RequestTime      time.Time
	HolderName       string
	Iban             string
	Country          string
}

//Select * from transaction where account_id = id

//type TransactionSend struct {
//	TransactionId uint `gorm:"primaryKey"`
//	ToAccountId   uint
//	Amount        float64
//	Currency      string
//}
//
//type TransactionExchange struct {
//	TransactionId uint            `gorm:"primaryKey"`
//	ToAccountId   uint            //account B (euro)
//	FromCurrency  string          //usd
//	ToCurrency    string          //euro
//	FromAmount    decimal.Decimal //100usd
//	ToAmount      decimal.Decimal //90euro
//	Rate          decimal.Decimal //1.15
//}
//
//type TransactionDeposit struct {
//	TransactionId uint            `gorm:"primaryKey"`
//	FromAccountId uint            //account B (euro)
//	FromCurrency  string          //usd
//	ToCurrency    string          //euro
//	FromAmount    decimal.Decimal //100usd
//	ToAmount      decimal.Decimal //90euro
//	Rate          decimal.Decimal
//}
