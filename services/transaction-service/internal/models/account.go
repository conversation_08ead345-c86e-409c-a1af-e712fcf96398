package models

import (
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Account struct {
	Entity
	Name              string
	OwnerId           uint
	Fees              []Fee
	CurrencyId        uint
	Currency          Currency
	BankId            *uint `gorm:"null"`
	Bank              Bank
	AssetType         string
	Status            string `gorm:"default:active"`
	AccountNumber     string `gorm:"unique;not null"`
	BankCountry       string
	BankNumber        string
	BankAddress       string
	BankAccountNumber string
	BranchCode        string
	AccountOwner      string
	Reference         string
	CurrentAmount     decimal.Decimal `gorm:"not null;default:0"`
	Iban              *Iban
	BicSwift          string
	QrCode            string
	AddressLink       string
}

func (a *Account) BeforeCreate(tx *gorm.DB) (err error) {
	var nextID int64
	// Use raw SQL to get the next sequence value
	err = tx.Raw("SELECT nextval('account_number_seq')").Scan(&nextID).Error
	if err != nil {
		return err
	}

	a.AccountNumber = fmt.Sprintf("%012d", nextID) // 12-digit zero-padded
	return nil
}
