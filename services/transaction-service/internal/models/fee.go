package models

import "github.com/shopspring/decimal"

type FeeCategory struct {
	Entity
	Name     string    //Application set up
	FeeTypes []FeeType `gorm:"constraint:OnDelete:CASCADE;"`
}

type FeeType struct {
	Entity
	FeeCategoryId uint
	Name          string //Maintenance fee
}

type Fee struct {
	Entity
	AccountId     uint    `gorm:"constraint:OnDelete:CASCADE;"`
	FeeTypeId     uint    `gorm:"constraint:OnDelete:CASCADE;"`
	FeeType       FeeType `gorm:"constraint:OnDelete:CASCADE;"`
	OperationType string
	FixedAmount   decimal.Decimal
	Percentage    decimal.Decimal
}
