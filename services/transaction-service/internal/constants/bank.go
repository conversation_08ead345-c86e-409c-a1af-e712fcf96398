package constants

const ApiAuto = "auto"
const ApiManual = "manual"

const StatusInactive = "inactive"
const StatusActive = "active"
const StatusProcessing = "processing"

var AllowedApis = []string{ApiAuto, ApiManual}
var AllowedStatuses = []string{StatusActive, StatusInactive, StatusProcessing}

func IsValidApi(api string) bool {
	for _, v := range AllowedApis {
		if v == api {
			return true
		}
	}
	return false
}

func IsValidStatus(status string) bool {
	for _, v := range AllowedStatuses {
		if v == status {
			return true
		}
	}
	return false
}
