package services

import (
	"transaction-service/core/config"
	"transaction-service/core/templates"
	"transaction-service/internal/repositories"
)

type Service struct {
	IbanServiceInterface
	BankServiceInterface
	CurrencyServiceInterface
	UserServiceInterface
	FeeServiceInterface
	AccountServiceInterface
	TransactionServiceInterface
	NotificationServiceInterface
}

func NewService(repos *repositories.Repository, conf config.Config, fileManager templates.FileManager) *Service {
	return &Service{
		IbanServiceInterface:     NewIbanService(repos.IbanRepositoryInterface),
		BankServiceInterface:     NewBankService(repos.BankRepositoryInterface),
		CurrencyServiceInterface: NewCurrencyService(repos.CurrencyRepositoryInterface),
		UserServiceInterface:     NewUserService(repos.UserRepositoryInterface),
		FeeServiceInterface:      NewFeeService(repos.FeeRepositoryInterface),
		AccountServiceInterface: NewAccountService(
			repos.AccountRepositoryInterface,
			repos.BankRepositoryInterface,
			repos.FeeRepositoryInterface,
			repos.CurrencyRepositoryInterface),
		TransactionServiceInterface:  NewTransactionService(repos.TransactionRepositoryInterface, repos.CurrencyRepositoryInterface, repos.AccountRepositoryInterface, fileManager),
		NotificationServiceInterface: NewNotificationService(),
	}
}
