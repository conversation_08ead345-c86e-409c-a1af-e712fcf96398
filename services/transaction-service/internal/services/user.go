package services

import "transaction-service/internal/repositories"

type UserServiceInterface interface {
	GetToken(username string) (string, error)
}

type UserService struct {
	repo repositories.UserRepositoryInterface
}

func NewUserService(repo repositories.UserRepositoryInterface) *UserService {
	return &UserService{
		repo: repo,
	}
}

func (us *UserService) GetToken(username string) (string, error) {
	return us.repo.GetToken(username)
}
