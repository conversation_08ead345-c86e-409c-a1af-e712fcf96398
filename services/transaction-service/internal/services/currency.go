package services

import (
	"transaction-service/internal/dto"
	"transaction-service/internal/models"
	"transaction-service/internal/repositories"
)

type CurrencyServiceInterface interface {
	CreateCurrency(input *dto.CurrencyDTO) error
	GetAll() ([]models.Currency, error)
	Update(id uint, input *dto.UpdateCurrencyDTO) error
	Delete(id uint) error

	CreateCurrencyExchange(currency *models.CurrencyExchange) error
	UpdateCurrencyExchange(id uint, update *dto.UpdateCurrencyExchange) (*models.CurrencyExchange, error)
	GetAllCurrencyExchange(query *dto.CurrencyExchangeGetAllQuery) ([]models.CurrencyExchange, error)
	DeleteCurrencyExchange(id uint) error
	GetCurrencyExchangeByIds(firstCurrencyId uint, secondCurrencyId uint) (*models.CurrencyExchange, error)
}

type CurrencyService struct {
	repo repositories.CurrencyRepositoryInterface
}

func NewCurrencyService(repo repositories.CurrencyRepositoryInterface) *CurrencyService {
	return &CurrencyService{
		repo: repo,
	}
}

func (cs *CurrencyService) CreateCurrency(input *dto.CurrencyDTO) error {
	bank := &models.Currency{
		CurrencyName: input.CurrencyName,
	}
	return cs.repo.CreateCurrency(bank)
}

func (cs *CurrencyService) GetAll() ([]models.Currency, error) {
	return cs.repo.GetAll()
}

func (cs *CurrencyService) Update(id uint, input *dto.UpdateCurrencyDTO) error {
	currency, err := cs.repo.GetOne(id)
	if err != nil {
		return nil
	}

	if input.CurrencyName != "" {
		currency.CurrencyName = input.CurrencyName
	}

	return cs.repo.Update(currency)
}

func (cs *CurrencyService) Delete(id uint) error {
	return cs.repo.Delete(id)
}

func (cs *CurrencyService) CreateCurrencyExchange(currency *models.CurrencyExchange) error {
	return cs.repo.CreateCurrencyExchange(currency)
}

func (cs *CurrencyService) UpdateCurrencyExchange(id uint, update *dto.UpdateCurrencyExchange) (*models.CurrencyExchange, error) {
	currencyExchange, err := cs.repo.GetOneCurrencyExchange(id)
	if err != nil {
		return nil, err
	}
	if !update.FirstCurrencySell.IsZero() {
		currencyExchange.FirstCurrencySell = update.FirstCurrencySell
	}
	if !update.SecondCurrencySell.IsZero() {
		currencyExchange.SecondCurrencySell = update.SecondCurrencySell
	}
	if err = cs.repo.UpdateCurrencyExchange(currencyExchange); err != nil {
		return nil, err
	}
	return currencyExchange, nil
}

func (cs *CurrencyService) GetAllCurrencyExchange(query *dto.CurrencyExchangeGetAllQuery) ([]models.CurrencyExchange, error) {
	return cs.repo.GetAllCurrencyExchange(query)
}

func (cs *CurrencyService) DeleteCurrencyExchange(id uint) error {
	return cs.repo.DeleteCurrencyExchange(id)
}

func (cs *CurrencyService) GetCurrencyExchangeByIds(firstCurrencyId uint, secondCurrencyId uint) (*models.CurrencyExchange, error) {
	return cs.repo.GetCurrencyExchangeByIds(firstCurrencyId, secondCurrencyId)
}
