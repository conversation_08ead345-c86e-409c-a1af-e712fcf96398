package services

import (
	"fmt"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/models"
	"transaction-service/internal/repositories"
)

type BankServiceInterface interface {
	CreateBank(input *dto.BankDTO) error
	GetAll() ([]models.Bank, error)
	Update(id uint, input *dto.UpdateBankDTO) error
	Delete(id uint) error
}

type BankService struct {
	repo repositories.BankRepositoryInterface
}

func NewBankService(repo repositories.BankRepositoryInterface) *BankService {
	return &BankService{
		repo: repo,
	}
}

func (bs *BankService) CreateBank(input *dto.BankDTO) error {

	if !constants.IsValidStatus(input.Status) {
		return fmt.Errorf("invalid status: %s", input.Status)
	}
	if !constants.IsValidApi(input.Api) {
		return fmt.Errorf("invalid API type: %s", input.Api)
	}

	bank := &models.Bank{
		BankName: input.BankName,
		Status:   input.Status,
		Api:      input.Api,
	}
	return bs.repo.CreateBank(bank)
}

func (bs *BankService) GetAll() ([]models.Bank, error) {
	return bs.repo.GetAll()
}

func (bs *BankService) Update(id uint, input *dto.UpdateBankDTO) error {
	bank, err := bs.repo.GetOne(id)
	if err != nil {
		return err
	}

	if input.BankName != "" {
		bank.BankName = input.BankName
	}
	if input.Status != "" {
		if !constants.IsValidStatus(input.Status) {
			return fmt.Errorf("invalid status: %s", input.Status)
		}
		bank.Status = input.Status
	}
	if input.Api != "" {
		if !constants.IsValidApi(input.Api) {
			return fmt.Errorf("invalid API type: %s", input.Api)
		}
		bank.Api = input.Api
	}

	return bs.repo.Update(bank)
}

func (bs *BankService) Delete(id uint) error {
	return bs.repo.Delete(id)
}
