package services

import (
	"transaction-service/internal/dto"
	"transaction-service/internal/models"
	"transaction-service/internal/repositories"
	"transaction-service/utils"
)

type IbanServiceInterface interface {
	CreateIban(d *dto.IbanDTO) error
	GetAll(pag *utils.Pagination) ([]models.Iban, int64, error)
	Update(id uint, input *dto.UpdateIbanDTO) error
	Delete(id uint) error
}

type IbanService struct {
	repo repositories.IbanRepositoryInterface
}

func NewIbanService(repo repositories.IbanRepositoryInterface) *IbanService {
	return &IbanService{
		repo: repo,
	}
}

func (is *IbanService) CreateIban(input *dto.IbanDTO) error {
	iban := &models.Iban{
		AccountId:   input.AccountId,
		IBAN:        input.IBAN,
		Description: input.Description,
		CurrencyId:  input.CurrencyId,
	}
	return is.repo.CreateIban(iban)
}

func (is *IbanService) GetAll(pag *utils.Pagination) ([]models.Iban, int64, error) {
	return is.repo.GetAll(pag)
}

func (is *IbanService) Update(id uint, input *dto.UpdateIbanDTO) error {
	iban, err := is.repo.GetOne(id)
	if err != nil {
		return err
	}

	if input.AccountId != 0 {
		iban.AccountId = input.AccountId
	}
	if input.IBAN != "" {
		iban.IBAN = input.IBAN
	}
	if input.Description != "" {
		iban.Description = input.Description
	}
	if input.CurrencyId != 0 {
		iban.CurrencyId = input.CurrencyId
	}

	return is.repo.Update(iban)
}

func (is *IbanService) Delete(id uint) error {
	return is.repo.Delete(id)
}
