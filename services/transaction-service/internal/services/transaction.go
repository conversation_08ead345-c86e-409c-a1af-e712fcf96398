package services

import (
	"errors"
	"time"
	"transaction-service/core/config"
	"transaction-service/core/connect"
	"transaction-service/core/templates"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/integration/rest"
	"transaction-service/internal/models"
	"transaction-service/internal/repositories"

	"github.com/Sebastiaan<PERSON><PERSON>pert/go-wkhtmltopdf"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type TransactionServiceInterface interface {
	Exchange(userId uint, role string, createExchange *dto.CreateExchangeDto) error
	GetAllTransaction(query *dto.TransactionQueryDto, pag *dto.PaginationDto) (*dto.GetAllTransactionResp, error)
	GetOneTransaction(id uint) (*models.Transaction, error)
	UpdateTransactionStatus(id uint, status string) error
	GetExchangeAmount(exchangeAmountDto *dto.ExchangeAmountDto) (decimal.Decimal, error)
	GetExchangeFeeAmount(feeAmountDto *dto.GetFeeAmountDto) (decimal.Decimal, error)
	GetTransactionDetails(id uint) (*dto.TransactionDetailsDto, error)
	GetRecipientsByOwnerId(ownerId uint) ([]models.Recipient, error)
	Send(sendTransactionDto *dto.TransactionSendDto, userId uint, role string) error
	GetSendFeeAmount(sendAmountDto *dto.SendAmountDto) (decimal.Decimal, error)
	AddDeposit(addDeposit *dto.AddDepositDto) error
	DownloadTransactionPDF(transactionId uint) (*wkhtmltopdf.PDFGenerator, error)
}

type TransactionService struct {
	repo         repositories.TransactionRepositoryInterface
	currencyRepo repositories.CurrencyRepositoryInterface
	accountRepo  repositories.AccountRepositoryInterface
	fileManager  templates.FileManager
}

func NewTransactionService(repo repositories.TransactionRepositoryInterface,
	currencyRepo repositories.CurrencyRepositoryInterface,
	accountRepo repositories.AccountRepositoryInterface,
	fileManager templates.FileManager,
) *TransactionService {
	return &TransactionService{
		repo:         repo,
		currencyRepo: currencyRepo,
		accountRepo:  accountRepo,
		fileManager:  fileManager,
	}
}

func (ts *TransactionService) GetTransactionDetails(id uint) (*dto.TransactionDetailsDto, error) {
	tx, err := ts.repo.GetOneTransaction(id)
	if err != nil {
		return nil, err
	}

	return &dto.TransactionDetailsDto{
		TransactionId: tx.TransactionId,
		SlipId:        tx.SlipId,
		Type:          tx.Type,
		Direction:     tx.Direction,
		Status:        tx.Status,
		RequestDate:   tx.RequestDate,

		UserId:   tx.UserId,
		UserRole: tx.UserRole,

		AccountId:            tx.AccountId,
		AccountName:          tx.Account.Name,
		AccountNumber:        tx.Account.AccountNumber,
		AccountCurrency:      tx.Account.Currency.CurrencyName,
		AccountCurrentAmount: tx.Account.CurrentAmount,

		FeeId:          tx.FeeId,
		FeeTypeName:    tx.Fee.FeeType.Name,
		FeeFixedAmount: tx.Fee.FixedAmount,
		FeePercentage:  tx.Fee.Percentage,

		ProcessingAmount:         tx.ProcessingAmount,
		ProcessingAmountCurrency: tx.ProcessingAmountCurrency,
		MerchantAmount:           tx.MerchantAmount,
		MerchantAmountCurrency:   tx.MerchantAmountCurrency,
	}, nil
}

func (ts *TransactionService) GetAllTransaction(query *dto.TransactionQueryDto, pag *dto.PaginationDto) (*dto.GetAllTransactionResp, error) {
	return ts.repo.GetAllTransaction(query, pag)
}

func (ts *TransactionService) GetOneTransaction(id uint) (*models.Transaction, error) {
	return ts.repo.GetOneTransaction(id)
}

func (ts *TransactionService) Exchange(userId uint, role string, createExchange *dto.CreateExchangeDto) error {
	fromAccount, err := ts.accountRepo.GetAccountById(createExchange.FromAccountId)
	if err != nil {
		return err
	}
	toAccount, err := ts.accountRepo.GetAccountById(createExchange.ToAccountId)
	if err != nil {
		return err
	}

	if createExchange.Amount.GreaterThan(fromAccount.CurrentAmount) {
		return errors.New("insufficient amount")
	}
	//todo implement fee select
	var applyFee models.Fee
	for _, v := range fromAccount.Fees {
		if v.OperationType == constants.TransactionTypeExchange {
			applyFee = v
			break
		}
	}

	var feeAmount decimal.Decimal
	if !applyFee.Percentage.IsZero() {
		percentage, err := decimal.NewFromString("0.01")
		if err != nil {
			return errors.New("failed to initialize a new decimal value")
		}
		feeAmount = createExchange.Amount.Mul(applyFee.Percentage.Mul(percentage))
	}
	if !applyFee.FixedAmount.IsZero() {
		feeAmount = feeAmount.Add(applyFee.FixedAmount)
	}

	var processingAmount decimal.Decimal

	if fromAccount.Currency.CurrencyName == toAccount.Currency.CurrencyName {
		processingAmount = createExchange.Amount.Sub(feeAmount)
	} else {
		//extract currency exchange
		currencyExchange, err := ts.currencyRepo.GetCurrencyExchangeByIds(fromAccount.CurrencyId, toAccount.CurrencyId)
		if err != nil {
			return err
		}
		if currencyExchange.ID == 0 {
			currencyExchange, err = ts.currencyRepo.GetCurrencyExchangeByIds(toAccount.CurrencyId, fromAccount.CurrencyId)
			if err != nil {
				return err
			}
		}
		if currencyExchange.ID == 0 {
			return gorm.ErrRecordNotFound
		}

		if fromAccount.CurrencyId == currencyExchange.FirstCurrencyId {
			processingAmount = createExchange.Amount.Sub(feeAmount).Mul(currencyExchange.FirstCurrencySell)
		}
		if fromAccount.CurrencyId == currencyExchange.SecondCurrencyId {
			processingAmount = createExchange.Amount.Sub(feeAmount).Mul(currencyExchange.SecondCurrencySell)
		}
	}

	slipId, _ := uuid.NewRandom()
	transactionId, _ := uuid.NewRandom()
	var FromExchangeTransaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		AccountId:                fromAccount.ID,
		Direction:                constants.TransactionDirectionOut,
		Type:                     constants.TransactionTypeFiatExchangeWithdrawal,
		UserId:                   userId,
		UserRole:                 role,
		Status:                   constants.TransactionStatusProcessing,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		MerchantAmount:           createExchange.Amount.Sub(feeAmount),
		MerchantAmountCurrency:   fromAccount.Currency.CurrencyName,
		ProcessingAmount:         processingAmount,
		ProcessingAmountCurrency: toAccount.Currency.CurrencyName,
		RequestDate:              time.Now(),
	}

	if fromAccount.AssetType == constants.AccountAssetTypeCrypto {
		FromExchangeTransaction.Type = constants.TransactionTypeCryptoExchangeWithdrawal
	}
	transactionId, _ = uuid.NewRandom()
	var feeExchangeTransaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		Type:                     applyFee.FeeType.Name,
		AccountId:                fromAccount.ID,
		Direction:                constants.TransactionDirectionOut,
		UserId:                   userId,
		UserRole:                 role,
		Status:                   constants.TransactionStatusProcessing,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		MerchantAmount:           feeAmount,
		MerchantAmountCurrency:   fromAccount.Currency.CurrencyName,
		ProcessingAmount:         feeAmount,
		ProcessingAmountCurrency: fromAccount.Currency.CurrencyName,
		RequestDate:              time.Now(),
	}

	transactionId, _ = uuid.NewRandom()
	var ToExchangeTransaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		Direction:                constants.TransactionDirectionIn,
		Type:                     constants.TransactionTypeFiatExchangeDeposit,
		AccountId:                toAccount.ID,
		UserId:                   userId,
		UserRole:                 role,
		Status:                   constants.TransactionStatusProcessing,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		MerchantAmount:           createExchange.Amount.Sub(feeAmount),
		MerchantAmountCurrency:   fromAccount.Currency.CurrencyName,
		ProcessingAmount:         processingAmount,
		ProcessingAmountCurrency: toAccount.Currency.CurrencyName,
		RequestDate:              time.Now(),
	}

	if toAccount.AssetType == constants.AccountAssetTypeCrypto {
		ToExchangeTransaction.Type = constants.TransactionTypeCryptoExchangeDeposit
	}

	// Update fromAccount balance
	fromAccount.CurrentAmount = fromAccount.CurrentAmount.Sub(createExchange.Amount)

	// Save all transactions in a single DB transaction
	err = ts.repo.WithTransaction(func(tx *gorm.DB) error {
		if err = ts.repo.SaveTransaction(tx, &FromExchangeTransaction); err != nil {
			return err
		}
		if err = ts.repo.SaveTransaction(tx, &feeExchangeTransaction); err != nil {
			return err
		}
		if err = ts.repo.SaveTransaction(tx, &ToExchangeTransaction); err != nil {
			return err
		}
		if err = ts.accountRepo.UpdateAccountAmount(tx, fromAccount); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	var notification = dto.NotificationPayload{
		Type:    constants.NotificationTransactionAdded,
		Message: "New transaction added",
	}

	allAdmins, err := rest.FetchUserRoles()
	if err != nil {
		return err
	}
	for _, v := range allAdmins {
		notification.UserId = v.UserID
		notification.Role = v.Role
		connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationTransactionAdded, notification)
	}
	return nil
}

func (ts *TransactionService) UpdateTransactionStatus(id uint, status string) error {
	return ts.repo.UpdateTransactionStatus(id, status)
}

func (ts *TransactionService) GetExchangeAmount(exchangeAmountDto *dto.ExchangeAmountDto) (decimal.Decimal, error) {
	fromAccount, err := ts.accountRepo.GetAccountById(exchangeAmountDto.FromAccountId)
	if err != nil {
		return decimal.Zero, err
	}
	toAccount, err := ts.accountRepo.GetAccountById(exchangeAmountDto.ToAccountId)
	if err != nil {
		return decimal.Zero, err
	}
	currencyExchange, err := ts.currencyRepo.GetCurrencyExchangeByIds(fromAccount.CurrencyId, toAccount.CurrencyId)
	if err != nil {
		return decimal.Zero, err
	}
	if currencyExchange.ID == 0 {
		currencyExchange, err = ts.currencyRepo.GetCurrencyExchangeByIds(toAccount.CurrencyId, fromAccount.CurrencyId)
		if err != nil {
			return decimal.Zero, err
		}
	}
	if currencyExchange.ID == 0 {
		return decimal.Zero, gorm.ErrRecordNotFound
	}

	var processingAmount decimal.Decimal
	if currencyExchange.FirstCurrencyId == fromAccount.CurrencyId {
		processingAmount = exchangeAmountDto.Amount.Mul(currencyExchange.FirstCurrencySell)
	} else {
		processingAmount = exchangeAmountDto.Amount.Mul(currencyExchange.SecondCurrencySell)
	}
	return processingAmount, nil
}

func (ts *TransactionService) GetSendFeeAmount(sendAmountDto *dto.SendAmountDto) (decimal.Decimal, error) {
	account, err := ts.accountRepo.GetAccountById(sendAmountDto.AccountId)
	if err != nil {
		return decimal.Zero, err
	}

	var applyFee models.Fee
	for _, v := range account.Fees {
		if v.OperationType == constants.TransactionTypeSend {
			applyFee = v
			break
		}
	}
	var feeAmount decimal.Decimal
	if !applyFee.Percentage.IsZero() {
		percentage, err := decimal.NewFromString("0.01")
		if err != nil {
			return decimal.Zero, errors.New("failed to initialize a new decimal value")
		}
		feeAmount = sendAmountDto.Amount.Mul(applyFee.Percentage.Mul(percentage))
	}
	if !applyFee.FixedAmount.IsZero() {
		feeAmount = feeAmount.Add(applyFee.FixedAmount)
	}

	return feeAmount, nil
}

func (ts *TransactionService) GetExchangeFeeAmount(feeAmountDto *dto.GetFeeAmountDto) (decimal.Decimal, error) {
	fromAccount, err := ts.accountRepo.GetAccountById(feeAmountDto.FromAccountId)
	if err != nil {
		return decimal.Zero, err
	}

	var applyFee models.Fee
	for _, v := range fromAccount.Fees {
		if v.OperationType == constants.TransactionTypeExchange {
			applyFee = v
			break
		}
	}
	var feeAmount decimal.Decimal
	if !applyFee.Percentage.IsZero() {
		percentage, err := decimal.NewFromString("0.01")
		if err != nil {
			return decimal.Zero, errors.New("failed to initialize a new decimal value")
		}
		feeAmount = feeAmountDto.Amount.Mul(applyFee.Percentage.Mul(percentage))
	}
	if !applyFee.FixedAmount.IsZero() {
		feeAmount = feeAmount.Add(applyFee.FixedAmount)
	}

	return feeAmount, nil
}

func (ts *TransactionService) GetRecipientsByOwnerId(ownerId uint) ([]models.Recipient, error) {
	return ts.repo.GetRecipientsByOwnerId(ownerId)
}

func (ts *TransactionService) Send(sendTransactionDto *dto.TransactionSendDto, userId uint, role string) error {
	account, err := ts.accountRepo.GetAccountById(sendTransactionDto.AccountId)
	if err != nil {
		return err
	}

	if sendTransactionDto.Amount.GreaterThan(account.CurrentAmount) {
		return errors.New("insufficient amount")
	}

	var recipient models.Recipient
	if sendTransactionDto.RecipientId != 0 {
		r, err := ts.repo.GetRecipientById(sendTransactionDto.RecipientId)
		recipient = *r
		if err != nil {
			return err
		}
	} else {
		recipient.RecipientName = sendTransactionDto.RecipientName
		recipient.AccountNumber = sendTransactionDto.AccountNumber
		recipient.CompanyName = sendTransactionDto.CompanyName
		recipient.BankCountry = sendTransactionDto.BankCountry
		recipient.BicSwift = sendTransactionDto.BicSwift
		recipient.Email = sendTransactionDto.Email
		recipient.Description = sendTransactionDto.Description
		recipient.OwnerId = account.OwnerId
	}

	var applyFee models.Fee
	for _, v := range account.Fees {
		if v.OperationType == constants.TransactionTypeSend {
			applyFee = v
			break
		}
	}

	var feeAmount decimal.Decimal
	if !applyFee.Percentage.IsZero() {
		percentage, err := decimal.NewFromString("0.01")
		if err != nil {
			return errors.New("failed to initialize a new decimal value")
		}
		feeAmount = sendTransactionDto.Amount.Mul(applyFee.Percentage.Mul(percentage))
	}
	if !applyFee.FixedAmount.IsZero() {
		feeAmount = feeAmount.Add(applyFee.FixedAmount)
	}

	slipId, _ := uuid.NewRandom()
	transactionId, _ := uuid.NewRandom()
	var transaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		Type:                     constants.TransactionTypeFiatWithdrawal,
		Direction:                constants.TransactionDirectionOut,
		Status:                   constants.TransactionStatusProcessing,
		RequestDate:              time.Now(),
		UserId:                   userId,
		UserRole:                 role,
		AccountId:                account.ID,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		ProcessingAmount:         sendTransactionDto.Amount.Sub(feeAmount),
		ProcessingAmountCurrency: account.Currency.CurrencyName,
		MerchantAmount:           sendTransactionDto.Amount.Sub(feeAmount),
		MerchantAmountCurrency:   account.Currency.CurrencyName,
		Recipient:                &recipient,
		RecipientReference:       sendTransactionDto.Reference,
	}

	if account.AssetType == constants.AccountAssetTypeCrypto {
		transaction.Type = constants.TransactionTypeCryptoWithdrawal
	}

	transactionId, _ = uuid.NewRandom()
	var feeTransaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		Type:                     applyFee.FeeType.Name,
		AccountId:                account.ID,
		Direction:                constants.TransactionDirectionOut,
		UserId:                   userId,
		UserRole:                 role,
		Status:                   constants.TransactionStatusProcessing,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		MerchantAmount:           feeAmount,
		MerchantAmountCurrency:   account.Currency.CurrencyName,
		ProcessingAmount:         feeAmount,
		ProcessingAmountCurrency: account.Currency.CurrencyName,
		RequestDate:              time.Now(),
	}
	account.CurrentAmount = account.CurrentAmount.Sub(sendTransactionDto.Amount)

	// Save all transactions in a single DB transaction
	err = ts.repo.WithTransaction(func(tx *gorm.DB) error {
		if err = ts.repo.SaveTransaction(tx, &transaction); err != nil {
			return err
		}
		if err = ts.repo.SaveTransaction(tx, &feeTransaction); err != nil {
			return err
		}
		if err = ts.accountRepo.UpdateAccountAmount(tx, account); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	var notification = dto.NotificationPayload{
		Type:    constants.NotificationTransactionAdded,
		Message: "New transaction added",
	}

	allAdmins, err := rest.FetchUserRoles()
	if err != nil {
		return err
	}
	for _, v := range allAdmins {
		notification.UserId = v.UserID
		notification.Role = v.Role
		connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationTransactionAdded, notification)
	}
	return nil
}

func (ts *TransactionService) Deposit(accountId uint, depositDto *dto.TransactionDepositDto) (*dto.DepositDetailDto, error) {
	account, err := ts.accountRepo.GetAccountById(accountId)
	if err != nil {
		return nil, err
	}
	var depositDetail dto.DepositDetailDto
	if depositDto.Type == "swift" {
		var swift = dto.DepositSwiftDto{
			BankCountry:       account.BankCountry,
			BankAccountNumber: account.BankAccountNumber,
			BankAddress:       account.BankAddress,
			Reference:         account.Reference,
			BicSwift:          account.BicSwift,
		}
		depositDetail.SwiftDetails = &swift
	} else if depositDto.Type == "sepa" {
		var sepa = dto.DepositSepaDto{
			BicSwift:  account.BicSwift,
			Iban:      account.Iban.IBAN,
			Reference: account.Reference,
		}
		depositDetail.SepaDetails = &sepa
	} else if depositDto.Type == "crypto" {
		var crypto = dto.DepositCryptoDto{
			QrCode:      account.QrCode,
			AddressLink: account.AddressLink,
		}
		depositDetail.CryptoDetails = &crypto
	}
	return &depositDetail, nil
}

func (ts *TransactionService) AddDeposit(addDeposit *dto.AddDepositDto) error {
	account, err := ts.accountRepo.GetAccountById(addDeposit.AccountId)
	if err != nil {
		return err
	}

	var sender = models.Sender{
		Provider:         addDeposit.Provider,
		MerchantName:     addDeposit.MerchantName,
		MerchantEmail:    addDeposit.MerchantEmail,
		MerchantId:       addDeposit.MerchantId,
		ProcessingAmount: addDeposit.Amount,
		RequestTime:      addDeposit.RequestTime,
		HolderName:       addDeposit.HolderName,
		Iban:             addDeposit.Iban,
		Country:          addDeposit.Country,
	}

	var applyFee models.Fee
	for _, v := range account.Fees {
		if v.OperationType == constants.TransactionTypeDeposit {
			applyFee = v
			break
		}
	}

	var feeAmount decimal.Decimal
	if !applyFee.Percentage.IsZero() {
		percentage, err := decimal.NewFromString("0.01")
		if err != nil {
			return errors.New("failed to initialize a new decimal value")
		}
		feeAmount = addDeposit.Amount.Mul(applyFee.Percentage.Mul(percentage))
	}
	if !applyFee.FixedAmount.IsZero() {
		feeAmount = feeAmount.Add(applyFee.FixedAmount)
	}

	slipId, _ := uuid.NewRandom()
	transactionId, _ := uuid.NewRandom()
	var transaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		Type:                     constants.TransactionTypeFiatDeposit,
		Direction:                constants.TransactionDirectionIn,
		Status:                   constants.TransactionStatusCompleted,
		RequestDate:              addDeposit.RequestTime,
		UserId:                   account.OwnerId,
		UserRole:                 constants.OwnerRole,
		AccountId:                account.ID,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		ProcessingAmount:         addDeposit.Amount,
		ProcessingAmountCurrency: account.Currency.CurrencyName,
		Sender:                   &sender,
	}

	if account.AssetType == constants.AccountAssetTypeCrypto {
		transaction.Type = constants.TransactionTypeCryptoWithdrawal
	}

	transactionId, _ = uuid.NewRandom()
	var feeTransaction = models.Transaction{
		SlipId:                   slipId.String(),
		TransactionId:            transactionId.String(),
		Type:                     applyFee.FeeType.Name,
		AccountId:                account.ID,
		Direction:                constants.TransactionDirectionOut,
		UserId:                   account.OwnerId,
		UserRole:                 constants.OwnerRole,
		Status:                   constants.TransactionStatusCompleted,
		FeeId:                    applyFee.ID,
		Fee:                      applyFee,
		MerchantAmount:           feeAmount,
		MerchantAmountCurrency:   account.Currency.CurrencyName,
		ProcessingAmount:         feeAmount,
		ProcessingAmountCurrency: account.Currency.CurrencyName,
		RequestDate:              time.Now(),
	}

	account.CurrentAmount = account.CurrentAmount.Add(addDeposit.Amount.Sub(feeAmount))
	// Save all transactions in a single DB transaction
	err = ts.repo.WithTransaction(func(tx *gorm.DB) error {
		if err = ts.repo.SaveTransaction(tx, &transaction); err != nil {
			return err
		}
		if err = ts.repo.SaveTransaction(tx, &feeTransaction); err != nil {
			return err
		}
		if err = ts.accountRepo.UpdateAccountAmount(tx, account); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	var notification = dto.NotificationPayload{
		Type:    constants.NotificationTransactionSuccess,
		Message: "New transaction added",
		UserId:  account.OwnerId,
		Role:    constants.OwnerRole,
	}
	connect.RabbitMQ.Publish(constants.NotificationsExchange, constants.NotificationTransactionAdded, notification)

	return nil
}

func (ts *TransactionService) DownloadTransactionPDF(transactionId uint) (*wkhtmltopdf.PDFGenerator, error) {
	transaction, err := ts.repo.GetOneTransaction(transactionId)
	if err != nil {
		return nil, err
	}

	pdfG, err := ts.fileManager.MatchTemplateWithData(transaction, config.Get().TemplatePath.TransactionReceipt)
	if err != nil {
		return nil, err
	}

	return pdfG, nil
}
