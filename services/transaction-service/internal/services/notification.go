package services

import (
	"transaction-service/core/connect"
	"transaction-service/internal/dto"
	"transaction-service/internal/models"
)

type NotificationServiceInterface interface {
	AssembleNotification(transaction *models.Transaction, notification_type string, message string) dto.NotificationPayload
	Publish(exchange string, key string, notification dto.NotificationPayload)
}

type NotificationService struct {
}

func NewNotificationService() *NotificationService {
	return &NotificationService{}
}

func (n *NotificationService) AssembleNotification(transaction *models.Transaction, notification_type string, message string) dto.NotificationPayload {
	return dto.NotificationPayload{
		UserId:  transaction.UserId,
		Role:    transaction.UserRole,
		Type:    notification_type,
		Message: message,
		Transaction: &dto.NotificationTransactionPayload{
			AccountName:   transaction.Account.Name,
			OperationType: transaction.Type,
			Amount:        transaction.ProcessingAmount,
			Direction:     transaction.Direction,
		},
	}
}

func (n *NotificationService) Publish(exchange string, key string, notification dto.NotificationPayload) {
	_ = connect.RabbitMQ.Publish(exchange, key, &notification)
}
