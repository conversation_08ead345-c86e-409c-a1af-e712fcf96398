package services

import (
	"transaction-service/internal/models"
	"transaction-service/internal/repositories"
)

type FeeServiceInterface interface {
	GetFeeCategoryById(id uint) (*models.FeeCategory, error)
	GetAllFeeCategories() ([]models.FeeCategory, error)
	CreateFeeCategory(category *models.FeeCategory) error
	UpdateFeeCategory(category *models.FeeCategory) error
	DeleteFeeCategory(id uint) error

	GetFeeTypeById(id uint) (*models.FeeType, error)
	GetAllFeeTypes() ([]models.FeeType, error)
	CreateFeeType(ft *models.FeeType) error
	UpdateFeeType(ft *models.FeeType) error
	DeleteFeeType(id uint) error

	GetFeeById(id uint) (*models.Fee, error)
	CreateFee(fee *models.Fee) error
	UpdateFee(fee *models.Fee) error
	GetFeesByAccountID(accountID uint) ([]models.Fee, error)
	DeleteFee(id uint) error
}

type FeeService struct {
	repo repositories.FeeRepositoryInterface
}

func NewFeeService(repo repositories.FeeRepositoryInterface) *FeeService {
	return &FeeService{
		repo: repo,
	}
}

func (s *FeeService) GetFeeCategoryById(id uint) (*models.FeeCategory, error) {
	return s.repo.GetFeeCategoryById(id)
}
func (s *FeeService) GetFeeTypeById(id uint) (*models.FeeType, error) {
	return s.repo.GetFeeTypeById(id)
}
func (s *FeeService) GetFeeById(id uint) (*models.Fee, error) {
	return s.repo.GetFeeById(id)
}
func (s *FeeService) GetAllFeeCategories() ([]models.FeeCategory, error) {
	return s.repo.GetAllFeeCategories()
}

func (s *FeeService) CreateFeeCategory(category *models.FeeCategory) error {
	return s.repo.CreateFeeCategory(category)
}

func (s *FeeService) UpdateFeeCategory(category *models.FeeCategory) error {
	return s.repo.UpdateFeeCategory(category)
}

func (s *FeeService) DeleteFeeCategory(id uint) error {
	return s.repo.DeleteFeeCategory(id)
}

func (s *FeeService) GetAllFeeTypes() ([]models.FeeType, error) {
	return s.repo.GetAllFeeTypes()
}

func (s *FeeService) CreateFeeType(ft *models.FeeType) error {
	return s.repo.CreateFeeType(ft)
}

func (s *FeeService) UpdateFeeType(ft *models.FeeType) error {
	return s.repo.UpdateFeeType(ft)
}

func (s *FeeService) DeleteFeeType(id uint) error {
	return s.repo.DeleteFeeType(id)
}

func (s *FeeService) CreateFee(fee *models.Fee) error {
	return s.repo.CreateFee(fee)
}

func (s *FeeService) UpdateFee(fee *models.Fee) error {
	return s.repo.UpdateFee(fee)
}

func (s *FeeService) GetFeesByAccountID(accountID uint) ([]models.Fee, error) {
	return s.repo.GetFeesByAccountID(accountID)
}

func (s *FeeService) DeleteFee(id uint) error {
	return s.repo.DeleteFee(id)
}
