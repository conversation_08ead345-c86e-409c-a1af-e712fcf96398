package controllers

import (
	"bytes"
	"fmt"
	"net/http"
	"transaction-service/utils/parsers"

	"github.com/gin-gonic/gin"
)

type AppResp struct {
	Error   string
	Code    int
	Message string
}

type App<PERSON>andler func(ctx *gin.Context) *AppResp

func (a AppHandler) Handle(ctx *gin.Context) {
	if err := a(ctx); err != nil {
		ctx.JSON(err.Code, err)
	}
}

func Ok(ctx *gin.Context, i interface{}) *AppResp {
	ctx.JSON(http.StatusOK, i)
	return nil
}

func OkExcel(ctx *gin.Context, buff *bytes.Buffer, filename string) *AppResp {
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, filename))
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", buff.Bytes())
	return nil
}
func OkPdf(ctx *gin.Context, buff *bytes.Buffer, filename string) *AppResp {
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, filename))
	ctx.Header("Content-Type", "application/pdf")
	ctx.Data(http.StatusOK, "application/pdf", buff.Bytes())
	return nil
}

func BaseError(err string, code int) *AppResp {
	appError := &AppResp{
		Error: err,
		Code:  code,
	}

	return appError
}

func GetPager(ctx *gin.Context) (int, int) {
	page := parsers.ParamInt(ctx.Query("page"))
	pageSize := parsers.ParamInt(ctx.Query("page_size"))

	if page == 0 {
		page = 1
	}
	switch {
	case pageSize > 100:
		pageSize = 100
	case pageSize <= 0:
		pageSize = 10
	}

	return page, pageSize
}

type ErrBadRequest struct {
	Message string
}

func (b *ErrBadRequest) Error() string {
	return b.Message
}
