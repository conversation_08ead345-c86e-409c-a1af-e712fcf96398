package controllers

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/models"
	"transaction-service/internal/services"
	"transaction-service/utils/parsers"
)

type CurrencyController struct {
	service     services.CurrencyServiceInterface
	userService services.UserServiceInterface
}

func NewCurrencyController(service services.CurrencyServiceInterface, userService services.UserServiceInterface) *CurrencyController {
	return &CurrencyController{
		service:     service,
		userService: userService,
	}
}

func (nc *CurrencyController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)

	g.POST("create-currency", middleware.Authentication(nc.userService, constants.AdminRole), <PERSON><PERSON><PERSON><PERSON><PERSON>(nc.CreateCurrency).Handle)
	g.GET("get-all", middleware.Authentication(nc.userService, constants.AllRole), AppHandler(nc.GetAll).Handle)
	g.PATCH("update/:id", middleware.Authentication(nc.userService, constants.AdminRole), AppHandler(nc.Update).Handle)
	g.DELETE("delete/:id", middleware.Authentication(nc.userService, constants.AdminRole), AppHandler(nc.Delete).Handle)

	g.POST("create-currency-exchange", middleware.Authentication(nc.userService, constants.AdminRole), AppHandler(nc.CreateCurrencyExchange).Handle)
	g.GET("get-all-currency-exchanges", middleware.Authentication(nc.userService, constants.AllRole), AppHandler(nc.GetAllCurrencyExchanges).Handle)
	g.PATCH("update-currency-exchange/:id", middleware.Authentication(nc.userService, constants.AdminRole), AppHandler(nc.UpdateCurrencyExchange).Handle)
	g.DELETE("delete-currency-exchange/:id", middleware.Authentication(nc.userService, constants.AdminRole), AppHandler(nc.DeleteCurrencyExchange).Handle)
	return g
}

func (nc *CurrencyController) CreateCurrencyExchange(ctx *gin.Context) *AppResp {
	var currencyCreate models.CurrencyExchange
	if err := ctx.ShouldBindJSON(&currencyCreate); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := nc.service.CreateCurrencyExchange(&currencyCreate); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, currencyCreate)
}

func (nc *CurrencyController) GetAllCurrencyExchanges(ctx *gin.Context) *AppResp {
	var query dto.CurrencyExchangeGetAllQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	currencies, err := nc.service.GetAllCurrencyExchange(&query)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error getting currencies",
			Code:    http.StatusBadRequest,
		}
	}
	return Ok(ctx, currencies)
}

func (nc *CurrencyController) UpdateCurrencyExchange(ctx *gin.Context) *AppResp {
	var query dto.UpdateCurrencyExchange
	if err := ctx.ShouldBindJSON(&query); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	id := ctx.Param("id")
	currencyExchange, err := nc.service.UpdateCurrencyExchange(parsers.ParamUint(id), &query)
	if err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, currencyExchange)
}

func (nc *CurrencyController) DeleteCurrencyExchange(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")

	err := nc.service.DeleteCurrencyExchange(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "currency exchange deleted")
}

func (nc *CurrencyController) CreateCurrency(ctx *gin.Context) *AppResp {
	var input dto.CurrencyDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := nc.service.CreateCurrency(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error creating bank",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "currency created")
}

func (nc *CurrencyController) GetAll(ctx *gin.Context) *AppResp {
	currencies, err := nc.service.GetAll()
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error getting currencies",
			Code:    http.StatusBadRequest,
		}
	}
	return Ok(ctx, currencies)
}

func (nc *CurrencyController) Update(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	var input dto.UpdateCurrencyDTO

	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	currencyID := parsers.ParamUint(id)
	err := nc.service.Update(currencyID, &input)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "currency updated")
}

func (nc *CurrencyController) Delete(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")

	err := nc.service.Delete(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "currency deleted")
}
