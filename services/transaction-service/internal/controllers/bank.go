package controllers

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/services"
	"transaction-service/utils/parsers"
)

type BankController struct {
	service     services.BankServiceInterface
	userService services.UserServiceInterface
}

func NewBankController(service services.BankServiceInterface, userService services.UserServiceInterface) *BankController {
	return &BankController{
		service:     service,
		userService: userService,
	}
}

func (bc *BankController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)

	g.POST("create-bank", middleware.Authentication(bc.userService, constants.AdminRole), AppHandler(bc.CreateBank).Handle)
	g.GET("get-all", middleware.Authentication(bc.userService, constants.AdminRole), App<PERSON>andler(bc.GetAll).Handle)
	g.PATCH("update/:id", middleware.Authentication(bc.userService, constants.AdminRole), AppHandler(bc.Update).Handle)
	g.DELETE("delete/:id", middleware.Authentication(bc.userService, constants.AdminRole), AppHandler(bc.Delete).Handle)

	return g
}

func (bc *BankController) CreateBank(ctx *gin.Context) *AppResp {
	var input dto.BankDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := bc.service.CreateBank(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error creating bank",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "bank created")
}

func (bc *BankController) GetAll(ctx *gin.Context) *AppResp {
	banks, err := bc.service.GetAll()
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error getting banks",
			Code:    http.StatusBadRequest,
		}
	}
	return Ok(ctx, banks)
}

func (bc *BankController) Update(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	var input dto.UpdateBankDTO

	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	bankID := parsers.ParamUint(id)
	err := bc.service.Update(bankID, &input)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "bank updated")
}

func (bc *BankController) Delete(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")

	err := bc.service.Delete(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "bank deleted")
}
