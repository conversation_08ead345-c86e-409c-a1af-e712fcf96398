package controllers

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/models"
	"transaction-service/internal/services"
	"transaction-service/utils/parsers"
)

type FeeController struct {
	service     services.FeeServiceInterface
	userService services.UserServiceInterface
}

func NewFeeController(service services.FeeServiceInterface, userService services.UserServiceInterface) *FeeController {
	return &FeeController{
		service:     service,
		userService: userService,
	}
}

func (fc *FeeController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)

	g.GET("fee-categories", middleware.Authentication(fc.userService, constants.AdminRole), App<PERSON>and<PERSON>(fc.GetAllFeeCategories).Handle)
	g.POST("fee-categories", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.CreateFeeCategory).Handle)
	g.PATCH("fee-categories/:id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.UpdateFeeCategory).Handle)
	g.DELETE("fee-categories/:id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.DeleteFeeCategory).Handle)

	g.GET("fee-types", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.GetAllFeeTypes).Handle)
	g.POST("fee-types", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.CreateFeeType).Handle)
	g.PATCH("fee-types/:id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.UpdateFeeType).Handle)
	g.DELETE("fee-types/:id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.DeleteFeeType).Handle)

	g.POST("fees", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.CreateFee).Handle)
	g.PATCH("fees/:id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.UpdateFee).Handle)
	g.GET("fees-by-account-id/:account_id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.GetFeesByAccountID).Handle)
	g.DELETE("fees/:id", middleware.Authentication(fc.userService, constants.AdminRole), AppHandler(fc.DeleteFee).Handle)

	return g
}

func (fc *FeeController) GetAllFeeCategories(ctx *gin.Context) *AppResp {
	feeCategories, err := fc.service.GetAllFeeCategories()
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "failed to get fee categories",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, feeCategories)
}

func (fc *FeeController) CreateFeeCategory(ctx *gin.Context) *AppResp {
	var input dto.CreateFeeCategoryDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	var feeCategory = models.FeeCategory{
		Name: input.Name,
	}
	if err := fc.service.CreateFeeCategory(&feeCategory); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee category created")
}

func (fc *FeeController) UpdateFeeCategory(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	var input dto.UpdateFeeCategoryDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	feeCategory, err := fc.service.GetFeeCategoryById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	if input.Name != "" {
		feeCategory.Name = input.Name
	}

	if err = fc.service.UpdateFeeCategory(feeCategory); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee category updated")
}

func (fc *FeeController) DeleteFeeCategory(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	if err := fc.service.DeleteFeeCategory(parsers.ParamUint(id)); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee category deleted")
}

func (fc *FeeController) GetAllFeeTypes(ctx *gin.Context) *AppResp {
	types, err := fc.service.GetAllFeeTypes()
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, types)
}

func (fc *FeeController) CreateFeeType(ctx *gin.Context) *AppResp {
	var input dto.CreateFeeTypeDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	var feeType = models.FeeType{
		FeeCategoryId: input.FeeCategoryId,
		Name:          input.Name,
	}

	_, err := fc.service.GetFeeCategoryById(input.FeeCategoryId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	if err = fc.service.CreateFeeType(&feeType); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee type created")
}

func (fc *FeeController) UpdateFeeType(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	feeType, err := fc.service.GetFeeTypeById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	var input dto.UpdateFeeTypeDto
	if err = ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	if input.FeeCategoryId != 0 {
		_, err = fc.service.GetFeeCategoryById(input.FeeCategoryId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return &AppResp{
					Error:   err.Error(),
					Message: "not found",
					Code:    http.StatusNotFound,
				}
			}
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
		feeType.FeeCategoryId = input.FeeCategoryId
	}
	if input.Name != "" {
		feeType.Name = input.Name
	}
	if err = fc.service.UpdateFeeType(feeType); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee type updated")
}

func (fc *FeeController) DeleteFeeType(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	if err := fc.service.DeleteFeeType(parsers.ParamUint(id)); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee type deleted")
}

func (fc *FeeController) CreateFee(ctx *gin.Context) *AppResp {
	var input dto.CreateFeeDto
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	var fee = models.Fee{
		FeeTypeId:     input.FeeTypeId,
		AccountId:     input.AccountId,
		FixedAmount:   input.FixedAmount,
		Percentage:    input.Percentage,
		OperationType: input.OperationType,
	}
	if err := fc.service.CreateFee(&fee); err != nil {
		if errors.Is(gorm.ErrForeignKeyViolated, err) {
			return &AppResp{
				Error:   err.Error(),
				Message: "bad request",
				Code:    http.StatusBadRequest,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee created")
}

func (fc *FeeController) UpdateFee(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	fee, err := fc.service.GetFeeById(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	var input dto.UpdateFeeDto
	if err = ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	if !input.FixedAmount.IsZero() {
		fee.FixedAmount = input.FixedAmount
	}
	if !input.Percentage.IsZero() {
		fee.Percentage = input.Percentage
	}

	if err = fc.service.UpdateFee(fee); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee updated")
}

func (fc *FeeController) GetFeesByAccountID(ctx *gin.Context) *AppResp {
	accountID := ctx.Param("account_id")

	fees, err := fc.service.GetFeesByAccountID(parsers.ParamUint(accountID))
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, fees)
}

func (fc *FeeController) DeleteFee(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")

	if err := fc.service.DeleteFee(parsers.ParamUint(id)); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "fee deleted")
}
