package controllers

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/services"
	"transaction-service/utils"
	"transaction-service/utils/parsers"
)

type IbanController struct {
	service     services.IbanServiceInterface
	userService services.UserServiceInterface
}

func NewIbanController(service services.IbanServiceInterface, userService services.UserServiceInterface) *IbanController {
	return &IbanController{
		service:     service,
		userService: userService,
	}
}

func (ic *IbanController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)

	g.POST("create-iban", middleware.Authentication(ic.userService, constants.AdminRole), AppHandler(ic.CreateIban).Handle)
	g.GET("get-all", middleware.Authentication(ic.userService, constants.AdminRole), AppHandler(ic.GetAll).Handle)
	g.PATCH("update/:id", middleware.Authentication(ic.userService, constants.AdminRole), AppHandler(ic.Update).Handle)
	g.DELETE("delete/:id", middleware.Authentication(ic.userService, constants.AdminRole), AppHandler(ic.Delete).Handle)

	return g
}

func (ic *IbanController) CreateIban(ctx *gin.Context) *AppResp {
	var input dto.IbanDTO
	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := ic.service.CreateIban(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error creating iban",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "iban created")
}

func (ic *IbanController) GetAll(ctx *gin.Context) *AppResp {
	var pag utils.Pagination
	if err := ctx.ShouldBind(&pag); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "invalid pagination query",
			Code:    http.StatusBadRequest,
		}
	}
	pag.Normalize()

	ibans, total, err := ic.service.GetAll(&pag)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "error getting iban's",
			Code:    http.StatusInternalServerError,
		}
	}

	return Ok(ctx, dto.IbanListResponse{
		Total: total,
		Data:  ibans,
	})
}

func (ic *IbanController) Update(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	var input dto.UpdateIbanDTO

	if err := ctx.ShouldBindJSON(&input); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	ibanID := parsers.ParamUint(id)
	err := ic.service.Update(ibanID, &input)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "iban updated")
}

func (ic *IbanController) Delete(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")

	err := ic.service.Delete(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "iban deleted")
}
