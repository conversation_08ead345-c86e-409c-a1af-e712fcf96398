package controllers

import (
	"net/http"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/services"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	service        services.UserServiceInterface
	accountService services.AccountServiceInterface
}

func NewUserController(service services.UserServiceInterface, accountService services.AccountServiceInterface) *UserController {
	return &UserController{
		service: service,
		accountService: accountService,
		
	}
}

func (uc *UserController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	g.DELETE("me", middleware.Authentication(uc.service, constants.AllRole), AppHandler(uc.DeleteMyProfile).Handle)
	return g
}

func (uc *UserController) DeleteMyProfile(ctx *gin.Context) *AppResp {
	userId, _ := ctx.Get("user_id")
	var queryInput dto.AccountOwnQuery
	if err := ctx.ShouldBindQuery(&queryInput); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	accounts, err := uc.accountService.GetOwnAccounts(userId.(uint), &queryInput)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	for _, account := range accounts {
		err = uc.accountService.DeleteAccountById(account.ID)
		if err != nil {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
	}

	return Ok(ctx, "deleted successfully")
}
