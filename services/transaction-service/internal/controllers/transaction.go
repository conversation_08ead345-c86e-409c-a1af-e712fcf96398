package controllers

import (
	"bytes"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"transaction-service/internal/constants"
	"transaction-service/internal/dto"
	"transaction-service/internal/middleware"
	"transaction-service/internal/services"
	"transaction-service/utils/parsers"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TransactionController struct {
	service             services.TransactionServiceInterface
	userService         services.UserServiceInterface
	accountService      services.AccountServiceInterface
	currencyService     services.CurrencyServiceInterface
	notificationService services.NotificationServiceInterface
}

func NewTransactionController(service services.TransactionServiceInterface,
	userService services.UserServiceInterface,
	accountService services.AccountServiceInterface,
	currencyService services.CurrencyServiceInterface,
	notificationService services.NotificationServiceInterface,
) *TransactionController {
	return &TransactionController{
		service:             service,
		userService:         userService,
		accountService:      accountService,
		currencyService:     currencyService,
		notificationService: notificationService,
	}
}

func (tc *TransactionController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)

	g.POST("exchange", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.Exchange).Handle)
	g.GET("get-all", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetAllTransactions).Handle)
	g.POST("complete/:id", middleware.Authentication(tc.userService, constants.SuperAdminRole), AppHandler(tc.CompleteTransaction).Handle)
	g.POST("cancel/:id", middleware.Authentication(tc.userService, constants.SuperAdminRole), AppHandler(tc.CancelTransaction).Handle)
	g.GET("get-exchange-amount", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetExchangeAmount).Handle)
	g.GET("get-fee-amount", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetFeeAmount).Handle)
	g.GET("get-transaction-details/:id", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetTransactionDetail).Handle)
	g.GET("get-recipients/:account_id", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetRecipients).Handle)
	g.POST("send", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.Send).Handle)
	g.GET("get-send-fee-amount", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetSendFeeAmount).Handle)
	g.GET("deposit/:account_id", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.Deposit).Handle)
	g.POST("add-deposit", middleware.Authentication(tc.userService, constants.SuperAdminRole), AppHandler(tc.AddDeposit).Handle)
	g.GET("get-excel-report", middleware.Authentication(tc.userService, constants.SuperAdminRole), AppHandler(tc.GetExcelReport).Handle)
	g.GET("get-pdf-receipt/:transaction_id", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.GetTransactionReceiptPDF).Handle)
	g.POST("autonomous-exchange", middleware.Authentication(tc.userService, constants.AllRole), AppHandler(tc.AutonomousExchange).Handle)

	return g
}

// GetTransactionDetail Transaction Details
func (tc *TransactionController) GetTransactionDetail(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")

	details, err := tc.service.GetTransactionDetails(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, details)
}

func (tc *TransactionController) Exchange(ctx *gin.Context) *AppResp {
	var createExchange dto.CreateExchangeDto
	if err := ctx.ShouldBindJSON(&createExchange); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	userId, _ := ctx.Get("user_id")
	role, _ := ctx.Get("role")

	if err := tc.service.Exchange(userId.(uint), role.(string), &createExchange); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		} else if err.Error() == "insufficient amount" {
			return &AppResp{
				Error:   err.Error(),
				Message: "bad request",
				Code:    http.StatusBadRequest,
			}
		} else {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
	}
	return Ok(ctx, "success")
}

func (tc *TransactionController) GetAllTransactions(ctx *gin.Context) *AppResp {
	var query dto.TransactionQueryDto
	if err := ctx.ShouldBindQuery(&query); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusInternalServerError,
		}
	}
	var pag dto.PaginationDto
	if err := ctx.ShouldBindQuery(&pag); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusInternalServerError,
		}
	}

	pag.Default()
	resp, err := tc.service.GetAllTransaction(&query, &pag)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, resp)
}

func (tc *TransactionController) CompleteTransaction(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	transaction, err := tc.service.GetOneTransaction(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	// Transaction operation
	if transaction.Status != constants.TransactionStatusProcessing {
		return &AppResp{
			Error:   "transaction is not processing",
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err = tc.service.UpdateTransactionStatus(transaction.ID, constants.TransactionStatusCompleted); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	if transaction.Direction == constants.TransactionDirectionIn {
		account, err := tc.accountService.GetAccountById(transaction.AccountId)
		if err != nil {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
		newAmount := account.CurrentAmount.Add(transaction.ProcessingAmount)
		if err := tc.accountService.UpdateAmount(transaction.AccountId, newAmount); err != nil {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
	}
	//notification
	var notification = tc.notificationService.AssembleNotification(transaction, constants.NotificationTransactionSuccess, "notification completed")
	if transaction.Direction == constants.TransactionDirectionOut {
		notification.Transaction.Amount = transaction.MerchantAmount
		notification.Transaction.Currency = transaction.MerchantAmountCurrency
	}
	tc.notificationService.Publish(constants.NotificationsExchange, constants.NotificationTransactionSuccess, notification)
	return Ok(ctx, "ok")
}

func (tc *TransactionController) CancelTransaction(ctx *gin.Context) *AppResp {
	id := ctx.Param("id")
	transaction, err := tc.service.GetOneTransaction(parsers.ParamUint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	if transaction.Status != constants.TransactionStatusProcessing {
		return &AppResp{
			Error:   "transaction is not processing",
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	// Transaction operation
	if err = tc.service.UpdateTransactionStatus(transaction.ID, constants.TransactionStatusFailed); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	if transaction.Direction == constants.TransactionDirectionOut {
		account, err := tc.accountService.GetAccountById(transaction.AccountId)
		if err != nil {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
		//var feeAmount decimal.Decimal
		//if !transaction.Fee.Percentage.IsZero() {
		//	percentage, err := decimal.NewFromString("0.01")
		//	if err != nil {
		//		return &AppResp{
		//			Error:   err.Error(),
		//			Message: "internal server error",
		//			Code:    http.StatusInternalServerError,
		//		}
		//	}
		//	feeAmount = transaction.MerchantAmount.Mul(transaction.Fee.Percentage.Mul(percentage))
		//}
		//if !transaction.Fee.FixedAmount.IsZero() {
		//	feeAmount = feeAmount.Add(transaction.Fee.FixedAmount)
		//}
		newAmount := account.CurrentAmount.Add(transaction.MerchantAmount)
		if err := tc.accountService.UpdateAmount(transaction.AccountId, newAmount); err != nil {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
	}
	//notification
	var notification = tc.notificationService.AssembleNotification(transaction, constants.NotificationTransactionFail, "transaction canceled")
	if transaction.Direction == constants.TransactionDirectionOut {
		notification.Transaction.Amount = transaction.MerchantAmount
		notification.Transaction.Currency = transaction.MerchantAmountCurrency
	}
	tc.notificationService.Publish(constants.NotificationsExchange, constants.NotificationTransactionFail, notification)
	return Ok(ctx, "ok")
}

func (tc *TransactionController) GetExchangeAmount(ctx *gin.Context) *AppResp {
	//todo implement check for account permission
	var createExchange dto.ExchangeAmountDto
	if err := ctx.ShouldBindJSON(&createExchange); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	amount, err := tc.service.GetExchangeAmount(&createExchange)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, amount)
}

func (tc *TransactionController) GetSendFeeAmount(ctx *gin.Context) *AppResp {
	//todo implement check for account permission
	var createSend dto.SendAmountDto
	if err := ctx.ShouldBindQuery(&createSend); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	amount, err := tc.service.GetSendFeeAmount(&createSend)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, amount)
}

func (tc *TransactionController) Send(ctx *gin.Context) *AppResp {
	var sendQuery dto.TransactionSendDto
	if err := ctx.ShouldBindJSON(&sendQuery); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := sendQuery.Validate(); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}

	userId, _ := ctx.Get("user_id")
	role, _ := ctx.Get("role")

	if err := tc.service.Send(&sendQuery, userId.(uint), role.(string)); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	return Ok(ctx, "success")
}

func (tc *TransactionController) GetFeeAmount(ctx *gin.Context) *AppResp {
	//todo implement check for account permission
	var createExchange dto.GetFeeAmountDto
	if err := ctx.ShouldBindQuery(&createExchange); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	feeAmount, err := tc.service.GetExchangeFeeAmount(&createExchange)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, feeAmount)
}

func (tc *TransactionController) GetExcelReport(ctx *gin.Context) *AppResp {
	var query dto.TransactionQueryDto
	if err := ctx.ShouldBindQuery(&query); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusInternalServerError,
		}
	}
	var pag dto.PaginationDto
	if err := ctx.ShouldBindQuery(&pag); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusInternalServerError,
		}
	}

	pag.Default()
	resp, err := tc.service.GetAllTransaction(&query, &pag)
	// resp.Data
	excelize := excelize.NewFile()

	for k, v := range resp.Data {
		row := strconv.Itoa(k + 2)
		excelize.SetCellValue("Sheet1", "A"+row, v.SlipId)
		excelize.SetCellValue("Sheet1", "B"+row, v.CreatedAt)
		excelize.SetCellValue("Sheet1", "C"+row, v.Type)
		excelize.SetCellValue("Sheet1", "D"+row, v.Fee.Percentage)
		excelize.SetCellValue("Sheet1", "E"+row, v.Direction)
		excelize.SetCellValue("Sheet1", "F"+row, v.Fee.FixedAmount)
		excelize.SetCellValue("Sheet1", "G"+row, v.ProcessingAmount.String()+" "+v.ProcessingAmountCurrency)
		excelize.SetCellValue("Sheet1", "H"+row, v.MerchantAmount.String()+" "+v.MerchantAmountCurrency)
		excelize.SetCellValue("Sheet1", "I"+row, v.Status)

	}

	excelize.SetCellValue("Sheet1", "A1", "Slip ID")
	excelize.SetCellValue("Sheet1", "B1", "Request time")
	excelize.SetCellValue("Sheet1", "C1", "Type")
	excelize.SetCellValue("Sheet1", "D1", "Fee Percentage")
	excelize.SetCellValue("Sheet1", "E1", "Direction")
	excelize.SetCellValue("Sheet1", "F1", "Fee Fixed Amount")
	excelize.SetCellValue("Sheet1", "G1", "Processing Amount")
	excelize.SetCellValue("Sheet1", "H1", "Merchant Amount")
	excelize.SetCellValue("Sheet1", "I1", "Status")

	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	var buff bytes.Buffer
	if err = excelize.Write(&buff); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	return OkExcel(ctx, &buff, "transactions.xlsx")
}

func (tc *TransactionController) GetRecipients(ctx *gin.Context) *AppResp {
	accountId := ctx.Param("account_id")
	account, err := tc.accountService.GetAccountById(parsers.ParamUint(accountId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "bad request",
				Code:    http.StatusBadRequest,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	recipients, err := tc.service.GetRecipientsByOwnerId(account.OwnerId)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, recipients)
}

func (tc *TransactionController) Deposit(ctx *gin.Context) *AppResp {
	accountId := ctx.Param("account_id")
	var depositDto dto.TransactionDepositDto
	if err := ctx.ShouldBindQuery(&depositDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	account, err := tc.accountService.GetAccountById(parsers.ParamUint(accountId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	var respDto dto.DepositDetailDto
	switch depositDto.Type {
	case "swift":
		var swift = dto.DepositSwiftDto{
			BankAccountNumber: account.BankAccountNumber,
			BicSwift:          account.BicSwift,
			BankCountry:       account.BankCountry,
			BankAddress:       account.BankAddress,
			Reference:         account.Reference,
		}
		if account.Iban != nil {
			swift.Iban = account.Iban.IBAN
		}
		respDto.SwiftDetails = &swift
	case "sepa":
		var sepa = dto.DepositSepaDto{
			BicSwift:  account.BicSwift,
			Reference: account.Reference,
		}
		if account.Iban != nil {
			sepa.Iban = account.Iban.IBAN
		}
		respDto.SepaDetails = &sepa
	case "crypto":
		var crypto = dto.DepositCryptoDto{
			QrCode:      account.QrCode,
			AddressLink: account.AddressLink,
		}
		respDto.CryptoDetails = &crypto
	}

	return Ok(ctx, respDto)
}

func (tc *TransactionController) AddDeposit(ctx *gin.Context) *AppResp {
	var depositDto dto.AddDepositDto
	if err := ctx.ShouldBindJSON(&depositDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := tc.service.AddDeposit(&depositDto); err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	return Ok(ctx, "ok")
}

func (tc *TransactionController) GetTransactionReceiptPDF(ctx *gin.Context) *AppResp {
	transactionIdString := ctx.Param("transaction_id")
	transactionId := parsers.ParamUint(transactionIdString)

	transaction, err := tc.service.GetOneTransaction(transactionId)
	if err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		}
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}

	if transaction.ID == 0 {
		return &AppResp{
			Error:   "not found",
			Message: "not found",
			Code:    http.StatusNotFound,
		}
	}

	pdfG, err := tc.service.DownloadTransactionPDF(transactionId)
	if err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "internal server error",
			Code:    http.StatusInternalServerError,
		}
	}
	filename := fmt.Sprintf("receipt_%s.pdf", transactionIdString)
	return OkPdf(ctx, pdfG.Buffer(), filename)
}

func (tc *TransactionController) AutonomousExchange(ctx *gin.Context) *AppResp {
	var createAutonomousExchangeDto dto.CreateExchangeDto
	userId, _ := ctx.Get("user_id")
	role, _ := ctx.Get("role")
	if err := ctx.ShouldBindJSON(&createAutonomousExchangeDto); err != nil {
		return &AppResp{
			Error:   err.Error(),
			Message: "bad request",
			Code:    http.StatusBadRequest,
		}
	}
	if err := tc.service.Exchange(userId.(uint), role.(string), &createAutonomousExchangeDto); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &AppResp{
				Error:   err.Error(),
				Message: "not found",
				Code:    http.StatusNotFound,
			}
		} else if err.Error() == "insufficient amount" {
			return &AppResp{
				Error:   err.Error(),
				Message: "bad request",
				Code:    http.StatusBadRequest,
			}
		} else {
			return &AppResp{
				Error:   err.Error(),
				Message: "internal server error",
				Code:    http.StatusInternalServerError,
			}
		}
	}
	return Ok(ctx, "success")
}
