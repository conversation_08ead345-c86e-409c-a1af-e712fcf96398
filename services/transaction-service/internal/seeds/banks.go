package seeds

import (
	"encoding/json"
	"transaction-service/internal/models"
)

type BankSeed struct {
	*GenericSeed
}

func NewBankSeed() *BankSeed {
	s := NewGenericSeed(models.Bank{})
	return &BankSeed{&s}
}

func (s *BankSeed) Seed() (Summary, error) {
	defer s.Summarize()
	data, err := s.LoadFixture()
	if err != nil {
		return s.Error(err)
	}

	var fixtureData map[string][]models.Bank
	if err = json.Unmarshal(data, &fixtureData); err != nil {
		return s.Error(err)
	}

	for _, bank := range fixtureData["data"] {
		var exists bool = s.Exists(map[string]interface{}{
			"bank_name": bank.BankName,
		})

		if exists {
			s.Summary.Exist++
			continue
		}

		r := s.Query.Create(&bank)
		if err := r.Error; err != nil {
			s.LogFail(err.Error())
			s.Summary.Errors++
			continue
		}

		s.Summary.Created += r.<PERSON>s<PERSON>ffected
	}

	return s.Summary, nil
}
