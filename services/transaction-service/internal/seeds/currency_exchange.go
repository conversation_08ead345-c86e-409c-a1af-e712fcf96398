package seeds

import (
	"encoding/json"
	"transaction-service/internal/models"
)

type CurrencyExchangeSeed struct {
	*GenericSeed
}

func NewCurrencyExchangeSeed() *CurrencyExchangeSeed {
	s := NewGenericSeed(models.CurrencyExchange{})
	return &CurrencyExchangeSeed{&s}
}

func (s *CurrencyExchangeSeed) Seed() (Summary, error) {
	defer s.Summarize()
	data, err := s.LoadFixture()
	if err != nil {
		return s.Error(err)
	}

	var fixtureData map[string][]models.CurrencyExchange
	if err = json.Unmarshal(data, &fixtureData); err != nil {
		return s.Error(err)
	}

	for _, currencyExchange := range fixtureData["data"] {
		var exists bool = s.Exists(map[string]interface{}{
			"first_currency_sell": currencyExchange.FirstCurrencySell,
		})

		if exists {
			s.Summary.Exist++
			continue
		}

		r := s.Query.Create(&currencyExchange)
		if err := r.Error; err != nil {
			s.Log<PERSON>ail(err.Error())
			s.Summary.Errors++
			continue
		}

		s.Summary.Created += r.RowsAffected
	}

	return s.Summary, nil
}
