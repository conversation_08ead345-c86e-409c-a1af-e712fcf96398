package seeds

import (
	"encoding/json"
	"transaction-service/internal/models"
)

type CurrencySeed struct {
	*GenericSeed
}

func NewCurrencySeed() *CurrencySeed {
	s := NewGenericSeed(models.Currency{})
	return &CurrencySeed{&s}
}

func (s *CurrencySeed) Seed() (Summary, error) {
	defer s.Summarize()
	data, err := s.LoadFixture()
	if err != nil {
		return s.Error(err)
	}

	var fixtureData map[string][]models.Currency
	if err = json.Unmarshal(data, &fixtureData); err != nil {
		return s.Error(err)
	}

	for _, currency := range fixtureData["data"] {
		var exists bool = s.Exists(map[string]interface{}{
			"id": currency.ID,
		})

		if exists {
			s.Summary.Exist++
			continue
		}

		r := s.Query.Create(&currency)
		if err := r.Error; err != nil {
			s.LogFail(err.Error())
			s.Summary.Errors++
			continue
		}

		s.Summary.Created += r.RowsAffected
	}

	return s.Summary, nil
}
