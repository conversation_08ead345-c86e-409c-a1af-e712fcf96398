package seeds

import (
	"encoding/json"
	"transaction-service/internal/models"
)

type FeeCategorySeed struct {
	*GenericSeed
}

func NewFeeCategorySeed() *FeeCategorySeed {
	s := NewGenericSeed(models.FeeCategory{})
	return &FeeCategorySeed{&s}
}

func (s *FeeCategorySeed) Seed() (Summary, error) {
	defer s.Summarize()
	data, err := s.LoadFixture()
	if err != nil {
		return s.Error(err)
	}

	var fixtureData map[string][]models.FeeCategory
	if err = json.Unmarshal(data, &fixtureData); err != nil {
		return s.Error(err)
	}

	for _, feeCategory := range fixtureData["data"] {
		var exists bool = s.Exists(map[string]interface{}{
			"id": feeCategory.ID,
		})

		if exists {
			s.Summary.Exist++
			continue
		}

		r := s.Query.Create(&feeCategory)
		if err := r.Error; err != nil {
			s.LogFail(err.Error())
			s.Summary.Errors++
			continue
		}

		s.Summary.Created += r.RowsAffected
	}

	return s.Summary, nil
}
