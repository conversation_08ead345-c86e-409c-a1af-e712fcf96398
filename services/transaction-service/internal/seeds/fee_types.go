package seeds

import (
	"encoding/json"
	"transaction-service/internal/models"
)

type FeeTypeSeed struct {
	*GenericSeed
}

func NewFeeTypeSeed() *FeeTypeSeed {
	s := NewGenericSeed(models.FeeType{})
	return &FeeTypeSeed{&s}
}

func (s *FeeTypeSeed) Seed() (Summary, error) {
	defer s.Summarize()
	data, err := s.LoadFixture()
	if err != nil {
		return s.Error(err)
	}

	var fixtureData map[string][]models.FeeType
	if err = json.Unmarshal(data, &fixtureData); err != nil {
		return s.Error(err)
	}

	for _, feeType := range fixtureData["data"] {
		var exists bool = s.Exists(map[string]interface{}{
			"id": feeType.Name,
		})

		if exists {
			s.Summary.Exist++
			continue
		}

		r := s.Query.Create(&feeType)
		if err := r.Error; err != nil {
			s.LogFail(err.Error())
			s.Summary.Errors++
			continue
		}

		s.Summary.Created += r.RowsAffected
	}

	return s.Summary, nil
}
