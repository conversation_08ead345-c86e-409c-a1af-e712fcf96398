package rest

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"transaction-service/core/config"
)

func FetchUserRoles() ([]AdminsDto, error) {
	// Create HTTP request
	req, err := http.NewRequest("GET", config.Get().AuthService.Host+"api/v1/user/get-all-admins", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add Authorization header
	req.Header.Set("Authorization", "Bearer "+config.Get().AuthService.Token)

	// Execute the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read and decode the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var admins []AdminsDto
	if err = json.Unmarshal(body, &admins); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	return admins, nil
}
