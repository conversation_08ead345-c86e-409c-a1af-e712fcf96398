package dto

import (
	"github.com/shopspring/decimal"
	"time"
)

type TransactionDetailsDto struct {
	TransactionId string    `json:"transaction_id"`
	SlipId        string    `json:"slip_id"`
	Type          string    `json:"type"`
	Direction     string    `json:"direction"`
	Status        string    `json:"status"`
	RequestDate   time.Time `json:"request_date"`

	UserId   uint   `json:"user_id"`
	UserRole string `json:"user_role"`

	// Account Info
	AccountId            uint            `json:"account_id"`
	AccountName          string          `json:"account_name"`
	AccountNumber        string          `json:"account_number"`
	AccountCurrency      string          `json:"account_currency"`
	AccountCurrentAmount decimal.Decimal `json:"account_current_amount"`

	// Fee Info
	FeeId          uint            `json:"fee_id,omitempty"`
	FeeTypeName    string          `json:"fee_type_name,omitempty"`
	FeeFixedAmount decimal.Decimal `json:"fee_fixed_amount,omitempty"`
	FeePercentage  decimal.Decimal `json:"fee_percentage,omitempty"`

	// Transaction Amounts
	ProcessingAmount         decimal.Decimal `json:"processing_amount"`
	ProcessingAmountCurrency string          `json:"processing_amount_currency"`
	MerchantAmount           decimal.Decimal `json:"merchant_amount"`
	MerchantAmountCurrency   string          `json:"merchant_amount_currency"`
}
