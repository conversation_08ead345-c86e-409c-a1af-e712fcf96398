package dto

import "github.com/shopspring/decimal"

type CreateFeeCategoryDto struct {
	Name string
}

type UpdateFeeCategoryDto struct {
	Name string
}

type CreateFeeTypeDto struct {
	FeeCategoryId uint
	Name          string
}

type UpdateFeeTypeDto struct {
	FeeCategoryId uint
	Name          string
}

type CreateFeeDto struct {
	AccountId     uint
	FeeTypeId     uint
	FixedAmount   decimal.Decimal
	Percentage    decimal.Decimal
	OperationType string
}

type UpdateFeeDto struct {
	FixedAmount decimal.Decimal
	Percentage  decimal.Decimal
}
