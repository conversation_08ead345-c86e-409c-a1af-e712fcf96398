package dto

import (
	"errors"
	"github.com/shopspring/decimal"
	"time"
	"transaction-service/internal/models"
)

type CreateExchangeDto struct {
	FromAccountId uint
	ToAccountId   uint
	Amount        decimal.Decimal
}

type TransactionQueryDto struct {
	Status    string
	AccountId uint
	Type      string
	Direction string
}

type GetAllTransactionResp struct {
	Count int64
	Data  []models.Transaction
}

type ExchangeAmountDto struct {
	FromAccountId uint
	ToAccountId   uint
	Amount        decimal.Decimal
}

type SendAmountDto struct {
	AccountId uint
	Amount    decimal.Decimal
}

type GetFeeAmountDto struct {
	FromAccountId uint
	ToAccountId   uint
	Amount        decimal.Decimal
}

type TransactionSendDto struct {
	AccountId     uint
	RecipientId   uint
	AccountNumber string
	Amount        decimal.Decimal
	BankCountry   string
	RecipientName string
	Description   string
	BicSwift      string
	CompanyName   string
	Reference     string
	Email         string
}

func (tsd *TransactionSendDto) Validate() error {
	if tsd.RecipientId == 0 {
		if tsd.BankCountry == "" || tsd.BicSwift == "" || tsd.Email == "" || tsd.CompanyName == "" || tsd.RecipientName == "" || tsd.Reference == "" || tsd.Amount.IsZero() || tsd.AccountNumber == "" {
			return errors.New("missed required fields")
		}
		return nil
	}
	return nil
}

type TransactionDepositDto struct {
	Type string
}

type DepositDetailDto struct {
	SwiftDetails  *DepositSwiftDto
	SepaDetails   *DepositSepaDto
	CryptoDetails *DepositCryptoDto
}
type DepositSwiftDto struct {
	BankAccountNumber string
	Iban              string
	BicSwift          string
	BankCountry       string
	BankAddress       string
	Reference         string
	//todo add additional fields
}

type DepositSepaDto struct {
	Iban      string
	BicSwift  string
	Reference string
	//todo add additional fields
}

type DepositCryptoDto struct {
	AddressLink string
	QrCode      string
	//todo add additional fields
}

type AddDepositDto struct {
	AccountId     uint
	Amount        decimal.Decimal
	Provider      string
	MerchantName  string
	MerchantId    string
	MerchantEmail string
	RequestTime   time.Time
	HolderName    string
	Iban          string
	Country       string
}
