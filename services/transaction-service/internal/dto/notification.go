package dto

import (
	"github.com/shopspring/decimal"
	"time"
)

type NotificationPayload struct {
	ID          string
	UserId      uint
	Type        string
	Message     string
	Role        string
	Transaction *NotificationTransactionPayload
	CreatedAt   time.Time
}

type NotificationTransactionPayload struct {
	AccountName   string
	Amount        decimal.Decimal
	OperationType string
	Direction     string
	Currency      string
}
