package parsers

import (
	"github.com/shopspring/decimal"
	"math"
	"strconv"
	"time"
)

func ParamInt(p string) int {
	val, err := strconv.Atoi(p)
	if err != nil {
		return 0
	}
	return val
}

func ParamDecimal(p string) decimal.Decimal {
	val, err := strconv.ParseFloat(p, 64)
	if err != nil {
		return decimal.NewFromInt(0)
	}
	return decimal.NewFromFloat(val)
}

func ParamFloat32(p string) float32 {
	val, _ := strconv.ParseFloat(p, 32)
	return float32(val)
}

func ParamUint(p string) uint {
	return uint(ParamInt(p))
}

func ParamBool(p string) bool {
	val, _ := strconv.ParseBool(p)
	return val
}

func FloatTimestampToTime(p float64) time.Time {
	sec, frac := math.Modf(p)
	secInt := int64(sec)
	nanoSecInt := int64(frac * 1e9) // 0.113603 * 1e9 = 113603000 nanoseconds
	t := time.Unix(secInt, nanoSecInt).UTC()
	return t
}
