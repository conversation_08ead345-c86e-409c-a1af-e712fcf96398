package main

import (
	"flag"
	"fmt"
	"strings"
	"transaction-service/core/config"
	"transaction-service/core/connect"
	"transaction-service/core/database"
	"transaction-service/core/redis"
	"transaction-service/core/server"
)

type flags []string

func (f *flags) String() string {
	return strings.Join(*f, ", ")
}

func (f *flags) Set(value string) error {
	*f = append(*f, value)
	return nil
}

func main() {
	var sources flags

	flag.Var(&sources, "s", "Specify the source for seeding")
	flag.Parse()

	conf, err := config.Load()
	if err != nil {
		fmt.Printf("err config.Load() %s\n", err)
		return
	}
	fmt.Println(conf.Database.Password)
	dbase, err := database.Connect(conf.Database)
	if err != nil {
		fmt.Printf("err db.Connect() %s\n", err)
		return
	}
	connect.DB = dbase
	rds := redis.New(conf.Redis)
	connect.Redis = rds

	server.Runner(sources)
}
