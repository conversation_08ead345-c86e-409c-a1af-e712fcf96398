package main

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"log"
	"os/signal"
	"syscall"
	"time"
	"transaction-service/core/connect"
	"transaction-service/core/server"
)

func main() {
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	s := server.Server{}
	time.Sleep(10 * time.Second)
	r, rabHandler, e := s.Init()
	if e != nil {
		fmt.Println(e.Error())
		return
	}
	// Run worker in a goroutine
	go func() {
		if err := rabHandler.Start(ctx); err != nil {
			connect.Logger.Fatal("Worker encountered an error", zap.Error(err))
		}
	}()

	s.Run(r)
	<-ctx.Done()

	stop()
	log.Println("Shutting down gracefully, press Ctrl+C again to force")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := s.Srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown: ", err)
	}

	log.Println("Server exiting")
}
