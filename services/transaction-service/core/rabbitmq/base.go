package rabbitmq

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	amqp "github.com/rabbitmq/amqp091-go"
	"transaction-service/core/config"
)

type RabbitMQ struct {
	conn *amqp.Connection
	ch   *amqp.Channel
}

func getDsn(conf config.RabbitMQ) string {
	return fmt.Sprintf("amqp://%s:%s@%s:%d/", conf.User, conf.Password, conf.Host, conf.Port)
}

func NewRabbitMq() *RabbitMQ {
	return &RabbitMQ{}
}

func (r *RabbitMQ) Init(conf config.RabbitMQ) error {
	var err error
	r.conn, err = amqp.Dial(getDsn(conf))
	if err != nil {
		return err
	}
	r.ch, err = r.conn.Channel()
	if err != nil {
		return err
	}
	return nil
}

func (r *RabbitMQ) Close() {
	_ = r.ch.Close()
	_ = r.conn.Close()
}

func (r *RabbitMQ) Exchange(name, exchangeType string) error {
	err := r.ch.ExchangeDeclare(
		name,
		exchangeType,
		false,
		false,
		false,
		false,
		nil,
	)
	return err
}

func (r *RabbitMQ) Queue(name string) (*amqp.Queue, error) {
	queue, err := r.ch.QueueDeclare(name,
		true,
		false,
		false,
		false,
		nil,
	)
	return &queue, err
}

func (r *RabbitMQ) Binding(queue, key, exchange string) error {
	err := r.ch.QueueBind(queue,
		key,
		exchange,
		false,
		nil,
	)
	return err
}

func (r *RabbitMQ) Publish(exchange, key string, data interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	err = r.ch.Publish(
		exchange,
		key,
		false,
		false,
		amqp.Publishing{
			DeliveryMode: amqp.Persistent,
			ContentType:  gin.MIMEJSON,
			Body:         jsonData,
		},
	)
	return err
}

func (r *RabbitMQ) Consume(queue, consumer string) (<-chan amqp.Delivery, error) {
	deliveries, err := r.ch.Consume(
		queue,
		consumer,
		false,
		false,
		false,
		false,
		nil)
	if err != nil {
		return nil, err
	}
	return deliveries, nil
}
