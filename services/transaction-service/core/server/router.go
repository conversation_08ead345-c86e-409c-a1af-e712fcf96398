package server

import (
	"transaction-service/internal/controllers"
	middleware2 "transaction-service/internal/middleware"
	"transaction-service/internal/services"

	"github.com/elliotchance/orderedmap"
	"github.com/gin-gonic/gin"
)

type Controller interface {
	Register(r *gin.RouterGroup, s string) *gin.RouterGroup
}

func SetupRoutesWithDeps(service *services.Service) *gin.Engine {

	e := gin.Default()
	e.MaxMultipartMemory = 8 << 20
	e.Use(middleware2.CORSMiddleware(), middleware2.Log)
	auth := e.Group("/api/v1")
	{
		r := orderedmap.NewOrderedMap()
		r.Set("iban", controllers.NewIbanController(service.IbanServiceInterface, service.UserServiceInterface))
		r.Set("bank", controllers.NewBankController(service.BankServiceInterface, service.UserServiceInterface))
		r.Set("currency", controllers.NewCurrencyController(service.CurrencyServiceInterface, service.UserServiceInterface))
		r.Set("fee", controllers.NewFeeController(service.FeeServiceInterface, service.UserServiceInterface))
		r.Set("transaction", controllers.NewTransactionController(
			service.TransactionServiceInterface,
			service.UserServiceInterface,
			service.AccountServiceInterface,
			service.CurrencyServiceInterface,
			service.NotificationServiceInterface,
		))
		r.Set("account", controllers.NewAccountController(service.AccountServiceInterface, service.UserServiceInterface))
		r.Set("user", controllers.NewUserController(service.UserServiceInterface, service.AccountServiceInterface))

		for g := r.Front(); g != nil; g = g.Next() {
			if c, ok := g.Value.(Controller); ok {
				c.Register(auth, g.Key.(string))
			}
		}
	}

	//mediaPath := config.Get().Dir.Media
	//e.Static("/media", mediaPath)

	return e
}
