package server

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"transaction-service/core/config"
	"transaction-service/core/connect"
	"transaction-service/core/database"
	"transaction-service/core/rabbitmq"
	"transaction-service/core/redis"
	"transaction-service/core/templates"
	"transaction-service/core/worker"
	"transaction-service/internal/repositories"
	"transaction-service/internal/services"
	"transaction-service/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server struct {
	Srv  *http.Server
	Test bool
}

func (s *Server) Init() (*gin.Engine, *worker.RabbitMQHandler, error) {
	conf, err := config.Load()
	if err != nil {
		fmt.Printf("err config.Load() %s\n", err)
		return nil, nil, err
	}
	logger.Init("app.log")
	fmt.Println(conf.Database.Password)
	//postgres
	dbase, err := database.Connect(conf.Database)
	if err != nil {
		fmt.Printf("err db.Connect() %s\n", err)
		return nil, nil, err
	}
	//init fileManager
	fileManager := templates.NewFileManager(conf.TemplatePath)

	//init redis
	rds := redis.New(conf.Redis)
	//init entities
	repo := repositories.NewRepository(dbase, rds)
	service := services.NewService(repo, *conf, *fileManager)

	//init rabbitmq
	rab := rabbitmq.NewRabbitMq()
	err = rab.Init(conf.RabbitMQ)
	if err != nil {
		fmt.Println(err.Error())
		fmt.Printf("err rabbitmq connect %s\n", err)
	}

	//init real logger
	loggerr, err := logger.NewLogger(config.Get().Dir.LogPath)
	if err != nil {
		fmt.Printf("error init log zap")
	}
	defer loggerr.Sync()

	rabHandler := worker.NewRabbitMQHandler(service, rab, loggerr)

	connect.DB = dbase
	connect.Redis = rds
	connect.RabbitMQ = rab
	connect.Logger = loggerr
	//routing api
	r := SetupRoutesWithDeps(service)

	return r, rabHandler, nil
}

func (s *Server) Run(r *gin.Engine) {
	if r == nil {
		return
	}

	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", config.Get().Server.Host, config.Get().Server.Port),
		Handler: r,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("run.seeder: %s\n", err)
		}
	}()

	s.Srv = srv
}

func CloseDb(db2 *gorm.DB) {
	sql, err := db2.DB()
	if err != nil {
		fmt.Printf("error get sql db %s\n", err)
		return
	}

	err = sql.Close()
	if err != nil {
		fmt.Printf("error sql db close %s\n", err)
		return
	}
}

func (s *Server) CloseAll() {
	d := connect.DB
	CloseDb(d)
}
