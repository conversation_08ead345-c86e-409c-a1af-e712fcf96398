package worker

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	rabbitmq2 "transaction-service/core/rabbitmq"
	services2 "transaction-service/internal/services"
)

type RabbitMQHandler struct {
	rab    *rabbitmq2.RabbitMQ
	logger *zap.Logger
}

func NewRabbitMQHandler(service *services2.Service, rab *rabbitmq2.RabbitMQ, logger *zap.Logger) *RabbitMQHandler {
	return &RabbitMQHandler{
		rab:    rab,
		logger: logger,
	}
}

func (rh *RabbitMQHandler) Start(ctx context.Context) error {

	err := rh.rab.Exchange(rabbitmq2.Exchange, "direct")
	if err != nil {
		return err
	}

	queue, err := rh.rab.Queue(rabbitmq2.Queue)
	if err != nil {
		return err
	}

	err = rh.rab.Binding(queue.Name, "routingKey", rabbitmq2.Exchange)
	if err != nil {
		return err
	}

	msgs, err := rh.rab.Consume(
		queue.Name,
		"consumer",
	)
	if err != nil {
		return fmt.Errorf("failed to register a consumer: %w", err)
	}
	rh.logger.Info("Worker started", zap.String("queue", queue.Name))

	go func() {
		for d := range msgs {
			rh.logger.Info("Received a message",
				zap.String("delivery_tag", fmt.Sprintf("%d", d.DeliveryTag)),
				zap.ByteString("body", d.Body),
			)
		}
	}()
	<-ctx.Done()
	rh.logger.Info("Shutting down worker...")
	return nil
}
