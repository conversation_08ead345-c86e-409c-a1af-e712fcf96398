package config

import "github.com/spf13/viper"

type Server struct {
	Host string
	Port string
}

type Database struct {
	Host     string
	Port     string
	Username string
	Password string
	Name     string
	SslMode  string
}

type Token struct {
	SecretKey string
}

type Redis struct {
	Address  string
	DB       int
	Password string
}

type RabbitMQ struct {
	Host     string
	Port     int
	User     string
	Password string
}

type Dir struct {
	Seeder  string
	LogPath string
}

type AuthService struct {
	Token string
	Host  string
}
type TemplatePath struct {
	RootPath           string
	TransactionReceipt string
}
type Config struct {
	TemplatePath
	AuthService
	Server
	Token
	Database
	Redis
	RabbitMQ
	Dir
}

var config Config

func Load() (*Config, error) {
	return loadFromFile(".")
}

func loadFromFile(path string) (*Config, error) {
	viper.AddConfigPath(path)
	viper.SetConfigName("config")
	viper.SetConfigType("json")

	err := viper.ReadInConfig()
	if err != nil {
		return nil, err
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func Get() *Config {
	return &config
}
