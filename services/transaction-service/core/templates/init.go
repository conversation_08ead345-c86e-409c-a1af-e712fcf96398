package templates

import (
	"bytes"
	"html/template"
	"path/filepath"
	"transaction-service/core/config"

	"github.com/Sebas<PERSON><PERSON>/go-wkhtmltopdf"
)

type FileManager struct {
	TemplatePath string
}

func NewFileManager(config config.TemplatePath) *FileManager {
	return &FileManager{
		TemplatePath: config.RootPath,
	}
}

func (f *FileManager) SetTepmlatePath(templatePath string) {
	f.TemplatePath = templatePath
}

func (f *FileManager) MatchTemplateWithData(data interface{}, templatePath string) (*wkhtmltopdf.PDFGenerator, error) {
	body, err := f.ParseTemplate(filepath.Join(f.TemplatePath, templatePath), data)
	if err != nil {
		return &wkhtmltopdf.PDFGenerator{}, err
	}

	// Create new PDF generator
	pdfG, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return &wkhtmltopdf.PDFGenerator{}, err
	}

	// Use in-memory buffer instead of file
	htmlReader := bytes.NewReader([]byte(body))
	page := wkhtmltopdf.NewPageReader(htmlReader)

	pdfG.AddPage(page)
	pdfG.PageSize.Set(wkhtmltopdf.PageSizeA4)
	pdfG.Dpi.Set(300)

	// Generate the PDF
	err = pdfG.Create()
	if err != nil {
		return &wkhtmltopdf.PDFGenerator{}, err
	}

	return pdfG, nil
}

func (f *FileManager) ParseTemplate(templateFileName string, data interface{}) (string, error) {
	t, err := template.ParseFiles(templateFileName)
	if err != nil {
		return "", err
	}
	buf := new(bytes.Buffer)
	if err = t.Execute(buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}
