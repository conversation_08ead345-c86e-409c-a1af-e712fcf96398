package security

import (
	"errors"
	"github.com/golang-jwt/jwt/v4"
	"time"
)

type TokenClaims struct {
	Email       string            `json:"email"`
	UserID      uint              `json:"user_id"`
	Role        string            `json:"role"`
	Permissions map[uint][]string `json:"permissions"`
	TokenType   string            `json:"token_type"`
	jwt.RegisteredClaims
}

const (
	AccessSecret  = "aafsdfwe23rfasdvrg9fvjwfwu4832k2masfgjouergrg"
	RefreshSecret = "nYREh1kgZbJfCj2Rhs4W2NmtetXlhng1rWiW5Vx0hg4="
	AccessExpiry  = 15 * time.Minute
	RefreshExpiry = 7 * 24 * time.Hour
)

func GenerateTokens(userID uint, email, role string, permissions map[uint][]string) (string, string, error) {
	accessToken, err := generateToken(userID, email, role, permissions, "access", AccessSecret, AccessExpiry)
	if err != nil {
		return "", "", err
	}
	refreshToken, err := generateToken(userID, email, "", nil, "refresh", RefreshSecret, RefreshExpiry)
	if err != nil {
		return "", "", err
	}
	return accessToken, refreshToken, nil
}

func generateToken(
	userID uint,
	email string,
	role string,
	permissions map[uint][]string,
	tokenType string,
	secret string,
	duration time.Duration,
) (string, error) {
	claims := TokenClaims{
		UserID:      userID,
		Email:       email,
		Role:        role,
		Permissions: permissions,
		TokenType:   tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(duration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

func ValidateToken2(tokenStr, secret, expectedType string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenStr, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(*TokenClaims)
	if !ok || claims.TokenType != expectedType {
		return nil, errors.New("invalid token type")
	}

	return claims, nil
}

func GetTTLFromToken(tokenStr string) time.Duration {
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("неверный метод подписи")
		}
		return AccessSecret, nil
	})

	if err != nil || !token.Valid {
		return 0
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return 0
	}

	expFloat, ok := claims["exp"].(float64)
	if !ok {
		return 0
	}

	expTime := time.Unix(int64(expFloat), 0)
	ttl := time.Until(expTime)
	if ttl < 0 {
		return 0
	}
	return ttl
}
