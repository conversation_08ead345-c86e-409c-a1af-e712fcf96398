package logger

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"log"
	"os"
)

var LogFile *os.File

func Init(fileName string) error {
	var err error

	// Открываем файл журнала
	LogFile, err = os.OpenFile(fileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}

	// Устанавливаем файл журнала как вывод для пакета log
	log.SetOutput(LogFile)

	// Возвращаем ошибку
	return nil
}

func NewLogger(logFilePath string) (*zap.Logger, error) {

	// lumberjack.Logger is a io.Writer that rolls over the log file when it
	// reaches a certain size. We’ll configure it to keep a limited number
	// of old log files, and optionally compress them.
	lumberJackLogger := &lumberjack.Logger{
		Filename:   logFilePath,
		MaxSize:    1,    // megabytes
		MaxBackups: 3,    // number of backups
		MaxAge:     1,    // days to retain
		Compress:   true, // whether to gzip the rotated files
	}

	// Create a zapcore that writes JSON logs to lumberjack’s rotating file.
	writeSyncer := zapcore.AddSync(lumberJackLogger)

	// Configure the encoder for structured JSON logs:
	encoderConfig := zap.NewProductionEncoderConfig()
	// Adjust time format, if desired:
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder // 2025-01-30T15:04:05Z
	jsonEncoder := zapcore.NewJSONEncoder(encoderConfig)

	// Set log level; InfoLevel, DebugLevel, etc.
	// For finance, Info or Warn might be good defaults.
	core := zapcore.NewCore(
		jsonEncoder,
		writeSyncer,
		zap.InfoLevel, // or zap.DebugLevel, etc.
	)

	// Finally, build a zap.Logger
	logger := zap.New(core, zap.AddCaller())

	// Optionally, log some info about the logger startup
	//logger.Info("Logger initialized",
	//	zap.String("file", logFilePath),
	//	zap.Int("max_size_mb", lumberJackLogger.MaxSize),
	//	zap.Int("max_backups", lumberJackLogger.MaxBackups),
	//	zap.Int("max_age_days", lumberJackLogger.MaxAge),
	//	zap.Bool("compress", lumberJackLogger.Compress),
	//)

	return logger, nil
}
