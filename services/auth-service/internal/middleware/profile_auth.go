package middleware

import (
	"auth-service/internal/constants"
	"auth-service/internal/models"
	"auth-service/internal/services"
	"auth-service/pkg/security"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"strings"
)

func ProfileMiddleware(
	profileSvc services.ProfileServiceInterface,
	allowedRoles ...string,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
			return
		}
		token := strings.TrimPrefix(authHeader, "Bearer ")
		claims, err := security.ValidateToken2(token, security.AccessSecret, "access")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			return
		}

		prof, err := profileSvc.GetProfileByEmail(claims.Email)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "profile not found"})
			return
		}

		c.Set("profile", prof)
		c.Set("email", claims.Email)
		c.Set("role", string(prof.Role))

		if !isRoleAllowed(string(prof.Role), allowedRoles...) {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "forbidden"})
			return
		}

		c.Next()
	}
}

func CheckPermission(requiredPermission constants.Permission) gin.HandlerFunc {
	return func(c *gin.Context) {
		profileInterface, exists := c.Get("profile")
		if !exists {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized"})
			return
		}

		profile, ok := profileInterface.(*models.Profile)
		if !ok {
			c.AbortWithStatusJSON(500, gin.H{"error": "Internal server error"})
			return
		}

		// Owner всегда имеет доступ
		if profile.Role == constants.CompanyOwner {
			c.Next()
			return
		}

		accountIDStr := c.Param("account_id")
		accountID, err := strconv.ParseUint(accountIDStr, 10, 64)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{"error": "Invalid account_id"})
			return
		}

		hasPermission := false
		for _, ap := range profile.AccountPermissions {
			if ap.AccountID == uint(accountID) && ap.Permission == string(requiredPermission) {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.AbortWithStatusJSON(403, gin.H{
				"error":   "Forbidden",
				"message": "Missing required permission: " + string(requiredPermission),
			})
			return
		}

		c.Next()
	}
}
