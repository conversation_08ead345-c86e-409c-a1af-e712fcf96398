package middleware

import (
	"auth-service/core/config"
	"auth-service/internal/constants"
	"auth-service/internal/services"
	"auth-service/pkg/security"
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"io"
	"net/http"
)

type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyWriter) Write(b []byte) (int, error) {
	n, err := w.ResponseWriter.Write(b)
	if err == nil {
		w.body.Write(b)
	}
	return n, err
}

type RequestLogData struct {
	Body         json.RawMessage     `json:"body"`
	Params       map[string]string   `json:"params"`
	Query        map[string][]string `json:"query"`
	FormDataBody string
}

// Authentication Middleware
func Authentication(serviceUser services.UserServiceInterface, allowedRoles ...string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		tokenString := security.ExtractToken(ctx.Request)
		claims, err := security.ValidateToken(tokenString, config.Get().Token.SecretKey)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"Error":   err.Error(),
				"Code":    http.StatusUnauthorized,
				"Message": "invalid token",
			})
			ctx.Abort()
			return
		}

		//storedToken, err := serviceUser.GetToken(claims.Username)
		//if err != nil {
		//	ctx.JSON(http.StatusUnauthorized, gin.H{
		//		"Error":   err.Error(),
		//		"Code":    http.StatusUnauthorized,
		//		"Message": "failed to get token",
		//	})
		//	ctx.Abort()
		//	return
		//}
		//if storedToken != tokenString {
		//	ctx.JSON(http.StatusUnauthorized, gin.H{
		//		"Error":   "invalid token",
		//		"Code":    http.StatusUnauthorized,
		//		"Message": "invalid token",
		//	})
		//	ctx.Abort()
		//	return
		//}

		ctx.Set("username", claims.Username)

		//user, err := serviceUser.GetByUsername(claims.Username)
		//if err != nil {
		//	ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
		//		"Error":   err.Error(),
		//		"Code":    http.StatusInternalServerError,
		//		"Message": "failed to get user",
		//	})
		//	return
		//}
		ctx.Set("user_id", claims.UserID)
		ctx.Set("role", claims.Role)
		if !isRoleAllowed(claims.Role, allowedRoles...) {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"Error":   "unauthorized",
				"Code":    http.StatusForbidden,
				"Message": "unauthorized",
			})
			ctx.Abort()
		}
		// Собираем тело запроса
		bodyBytes, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"Error":   err.Error(),
				"Code":    http.StatusInternalServerError,
				"Message": "internal server error",
			})
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		// Собираем параметры маршрута
		params := make(map[string]string)
		for _, param := range ctx.Params {
			params[param.Key] = param.Value
		}
		responseBody := new(bytes.Buffer)
		writer := &bodyWriter{body: responseBody, ResponseWriter: ctx.Writer}
		ctx.Writer = writer
		ctx.Next()
	}
}

func isRoleAllowed(role string, allowedRoles ...string) bool {
	if role == constants.SuperAdmin {
		return true
	}
	allowedRolesMap := make(map[string]bool)
	for _, allowedRole := range allowedRoles {
		allowedRolesMap[allowedRole] = true
	}

	if allowedRolesMap["all"] {
		return true
	}

	if role == constants.AdminRole {
		return true
	}
	if role == string(constants.CompanyOwner) {
		return true
	}

	if allowedRolesMap[role] {
		return true
	}

	return false
}
