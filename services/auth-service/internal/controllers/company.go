package controllers

import (
	"auth-service/core/config"
	"auth-service/internal/dto"
	"auth-service/internal/middleware"
	"auth-service/internal/models"
	"auth-service/internal/services"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"strings"
)

type CompanyController struct {
	service        services.CompanyServiceInterface
	profileService services.ProfileServiceInterface
}

func NewCompanyController(service services.CompanyServiceInterface, profileService services.ProfileServiceInterface) *CompanyController {
	return &CompanyController{
		service:        service,
		profileService: profileService,
	}
}

func (cc *CompanyController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	gAuth := g.Group("/", middleware.ProfileMiddleware(cc.profileService))
	gOwnerAdmin := g.Group("/", middleware.ProfileMiddleware(cc.profileService, "owner", "admin"))

	g.GET("/verify", cc.VerifyEmail)
	g.GET("get_one", cc.GetOneCompany)
	g.GET("get_all", cc.GetCompanies)
	gOwnerAdmin.GET("/profiles", cc.GetCompanyProfiles)

	g.POST("/register", cc.RegisterCompany)
	g.POST("/login", cc.LoginProfile)
	gAuth.POST("/logout", cc.LogoutCompany)
	g.POST("/refresh", cc.RefreshTokens)
	gAuth.POST("/delete", cc.DeleteCompany)

	gOwnerAdmin.POST("/:id/invite", cc.InviteUser)
	g.POST("/accept-invite", cc.AcceptInvite)

	return g
}

func (cc *CompanyController) RegisterCompany(ctx *gin.Context) {
	var req dto.CompanyRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := cc.service.CreateCompany(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Verification email sent"})
}

func (cc *CompanyController) VerifyEmail(ctx *gin.Context) {
	conf := config.Get()
	code := ctx.Query("code")
	if code == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "verification code is required"})
		return
	}

	accessToken, refreshToken, err := cc.service.VerifyEmail(code)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	redirectUrl := fmt.Sprintf("%s/auth-redirect/?access_token=%s&refresh_token=%s", conf.Server.FrontUrl, accessToken, refreshToken)
	ctx.Redirect(http.StatusFound, redirectUrl)
}

func (cc *CompanyController) LoginProfile(ctx *gin.Context) {
	var loginReq struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&loginReq); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	accessToken, refreshToken, err := cc.service.ProfileLogin(loginReq.Email, loginReq.Password)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"access_token":  accessToken,
		"refresh_token": refreshToken,
	})
}

func (cc *CompanyController) LogoutCompany(ctx *gin.Context) {
	email, _ := ctx.Get("email")
	accessToken := ctx.GetHeader("Authorization")

	tokenStr := strings.TrimPrefix(accessToken, "Bearer ")
	if err := cc.service.LogOut(email.(string), tokenStr); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"status": "200", "message": "successfully logout"})
}

func (cc *CompanyController) RefreshTokens(ctx *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	newAccess, newRefresh, err := cc.service.RefreshTokens(req.RefreshToken)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"access_token": newAccess, "refresh_token": newRefresh})
}

func (cc *CompanyController) DeleteCompany(ctx *gin.Context) {
	id := ctx.Query("id")
	if id == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "provide company id"})
		return
	}

	idUint, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid company_id"})
		return
	}

	companyID := uint(idUint)

	if err := cc.service.DeleteCompany(companyID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	}

	ctx.JSON(http.StatusOK, gin.H{"success": "company deleted successfully"})
}

func (cc *CompanyController) GetOneCompany(ctx *gin.Context) {
	idStr := ctx.Query("id")
	if idStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "company id is required"})
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid company id"})
		return
	}

	company, err := cc.service.GetCompany(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, company)
}

func (cc *CompanyController) GetCompanies(ctx *gin.Context) {
	status := ctx.Query("status")
	pageStr := ctx.DefaultQuery("page", "1")
	limitStr := ctx.DefaultQuery("limit", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid page"})
		return
	}
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid limit"})
		return
	}

	companies, total, err := cc.service.GetCompanies(status, page, limit)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":       companies,
		"total":      total,
		"page":       page,
		"limit":      limit,
		"totalPages": (total + int64(limit) - 1) / int64(limit),
	})
}

func (cc *CompanyController) InviteUser(ctx *gin.Context) {
	idParam := ctx.Param("id")
	companyID64, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid company id"})
		return
	}
	companyID := uint(companyID64)

	// 2) Считываем тело запроса в dto.InviteData
	var inviteDto dto.InviteData
	if err := ctx.ShouldBindJSON(&inviteDto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 3) Вызываем сервис: InviteUser
	token, err := cc.service.InviteUser(companyID, &inviteDto)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 4) Возвращаем только подтверждение, токен чаще не возвращают на клиент
	ctx.JSON(http.StatusOK, gin.H{
		"message": "invite sent",
		"token":   token,
	})
}

func (cc *CompanyController) AcceptInvite(ctx *gin.Context) {
	// 1) Привязываем JSON-тело к dto.SetPasswordRequest
	var req dto.SetPasswordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	accessToken, refreshToken, err := cc.profileService.AcceptInvite(&req)
	if err != nil {
		switch err.Error() {
		case "passwords do not match":
			ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		case "invalid or expired token":
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		default:
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// 3) Возвращаем фронтенду пару JWT-токенов
	ctx.JSON(http.StatusOK, gin.H{
		"access_token":  accessToken,
		"refresh_token": refreshToken,
	})
}

func (cc *CompanyController) GetCompanyProfiles(ctx *gin.Context) {
	profRaw, exists := ctx.Get("profile")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "profile not found"})
		return
	}

	profile := profRaw.(*models.Profile)

	profiles, err := cc.service.GetAllProfilesByCompanyId(profile.CompanyID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	responses := make([]dto.GetAllProfilesResponse, len(profiles))
	for i, prof := range profiles {
		permMap := make(map[uint][]string)
		for _, ap := range prof.AccountPermissions {
			permMap[ap.AccountID] = append(permMap[ap.AccountID], ap.Permission)
		}

		responses[i] = dto.GetAllProfilesResponse{
			ID:          prof.ID,
			Name:        prof.Name,
			Email:       prof.Email,
			PhoneNumber: prof.PhoneNumber,
			Address:     prof.Address,
			DateOfBirth: prof.DateOfBirth,
			Role:        string(prof.Role),
			Permissions: permMap,
		}
	}

	ctx.JSON(http.StatusOK, gin.H{"data": responses})
}
