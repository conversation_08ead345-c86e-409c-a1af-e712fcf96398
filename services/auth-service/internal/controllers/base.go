package controllers

import (
	"auth-service/utils/parsers"
	"github.com/gin-gonic/gin"
	"net/http"
)

type AppResp struct {
	Error   string
	Code    int
	Message string
}

type AppHandler func(ctx *gin.Context) *AppResp

func (a AppHandler) Handle(ctx *gin.Context) {
	if err := a(ctx); err != nil {
		ctx.JSON(err.Code, err)
	}
}

func Ok(ctx *gin.Context, i interface{}) *AppResp {
	ctx.JSON(http.StatusOK, i)
	return nil
}

func BaseError(err string, code int) *AppResp {
	appError := &AppResp{
		Error: err,
		Code:  code,
	}

	return appError
}

func GetPager(ctx *gin.Context) (int, int) {
	page := parsers.ParamInt(ctx.Query("page"))
	pageSize := parsers.ParamInt(ctx.Query("page_size"))

	if page == 0 {
		page = 1
	}
	switch {
	case pageSize > 100:
		pageSize = 100
	case pageSize <= 0:
		pageSize = 10
	}

	return page, pageSize
}
