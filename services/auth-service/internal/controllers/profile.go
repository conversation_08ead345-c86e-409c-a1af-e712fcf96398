package controllers

import (
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/middleware"
	"auth-service/internal/models"
	"auth-service/internal/services"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"net/http"
	"strconv"
)

type ProfileController struct {
	service     services.ProfileServiceInterface
	userService services.UserServiceInterface
}

func NewProfileController(service services.ProfileServiceInterface, userService services.UserServiceInterface) *ProfileController {
	return &ProfileController{
		service:     service,
		userService: userService,
	}
}

func (pc *ProfileController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	profileGroup := g.Group("", middleware.ProfileMiddleware(pc.service, constants.AllRole))
	adminRoute := g.Group("", middleware.Authentication(pc.userService, constants.AdminRole))
	profileOwner := g.Group("", middleware.ProfileMiddleware(pc.service, string(constants.CompanyOwner)))

	g.GET("/:id", pc.GetProfileById)
	adminRoute.GET("/owners", pc.GetAllOwners)
	profileGroup.GET("/me", pc.GetProfileMe)
	profileGroup.PATCH("/", pc.UpdateProfile)

	g.POST("/forgot-password", pc.ForgotPassword)
	g.POST("/reset-password", pc.ResetPassword)

	profileOwner.PATCH("/update-profile-owner", pc.UpdateProfileForOwner)

	return g
}

func (pc *ProfileController) GetProfileById(ctx *gin.Context) {
	idParam := ctx.Param("id")
	id, err := strconv.Atoi(idParam)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	profile, err := pc.service.GetProfileById(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Profile not found"})
		return
	}

	kycStatus := ""
	kybStatus := ""

	if profile.Company.KYC != nil {
		profile.Company.KYC.Status = constants.VerificationStatus(profile.Company.KYC.Status)
		kycStatus = string(profile.Company.KYC.Status)
	}

	if profile.Company.Kyb != nil {
		profile.Company.Kyb.Status = constants.VerificationStatus(profile.Company.Kyb.Status)
		kybStatus = string(profile.Company.Kyb.Status)
	}

	profileResponse := dto.ProfileResponseDto{
		Name:        profile.Name,
		Email:       profile.Email,
		PhoneNumber: profile.PhoneNumber,
		Address:     profile.Address,
		DateOfBirth: profile.DateOfBirth,
		Role:        profile.Role,
		VerificationStatuses: dto.VerificationStatus{
			KYCStatus: kycStatus,
			KYBStatus: kybStatus,
		},
	}

	ctx.JSON(http.StatusOK, profileResponse)
}

func (pc *ProfileController) UpdateProfile(c *gin.Context) {
	rawEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	email, ok := rawEmail.(string)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid email in context"})
		return
	}

	profile, err := pc.service.GetProfileByEmail(email)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Profile not found"})
		return
	}

	var input dto.UpdateProfileDto
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	err = pc.service.UpdateProfile(profile.ID, &input)
	if err != nil {
		if err.Error() == "no fields to update" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Profile updated successfully"})
}

func (pc *ProfileController) GetProfileMe(ctx *gin.Context) {
	rawID, exists := ctx.Get("email")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	email, ok := rawID.(string)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid profile_id in context"})
		return
	}

	profile, err := pc.service.GetProfileByEmail(email)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Profile not found"})
		return
	}

	kycStatus := ""
	kybStatus := ""

	if profile.Company.KYC != nil {
		profile.Company.KYC.Status = constants.VerificationStatus(profile.Company.KYC.Status)
		kycStatus = string(profile.Company.KYC.Status)
	}

	if profile.Company.Kyb != nil {
		profile.Company.Kyb.Status = constants.VerificationStatus(profile.Company.Kyb.Status)
		kybStatus = string(profile.Company.Kyb.Status)
	}

	profileResponse := dto.ProfileResponseDto{
		Name:        profile.Name,
		Email:       profile.Email,
		PhoneNumber: profile.PhoneNumber,
		Address:     profile.Address,
		DateOfBirth: profile.DateOfBirth,
		Role:        profile.Role,
		CompanyID:   profile.CompanyID,
		VerificationStatuses: dto.VerificationStatus{
			KYCStatus: kycStatus,
			KYBStatus: kybStatus,
		},
	}

	ctx.JSON(http.StatusOK, profileResponse)
}

func (pc *ProfileController) GetAllOwners(ctx *gin.Context) {
	pageStr := ctx.DefaultQuery("page", "1")
	limitStr := ctx.DefaultQuery("limit", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page <= 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid page"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid limit"})
		return
	}

	offset := (page - 1) * limit

	owners, err := pc.service.GetAllOwners(limit, offset)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, owners)
}

func (pc *ProfileController) GetAccountPermissions(c *gin.Context) {
	accountID, _ := strconv.ParseUint(c.Param("account_id"), 10, 32)
	profileID := c.MustGet("profileID").(uint)

	permissions, err := pc.service.GetAccountPermissions(profileID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get permissions"})
		return
	}

	var result []models.AccountPermission
	for _, p := range permissions {
		if p.AccountID == uint(accountID) {
			result = append(result, p)
		}
	}

	c.JSON(http.StatusOK, result)
}

func (pc *ProfileController) CheckAccountPermission(c *gin.Context) {
	accountID, _ := strconv.ParseUint(c.Query("account_id"), 10, 32)
	profileID, _ := strconv.ParseUint(c.Query("profile_id"), 10, 32)
	permission := c.Query("permission")

	hasPermission := pc.service.HasAccountPermission(
		uint(profileID),
		uint(accountID),
		permission,
	)

	c.JSON(http.StatusOK, gin.H{"has_permission": hasPermission})
}

func (pc *ProfileController) ForgotPassword(c *gin.Context) {
	var req dto.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email"})
		return
	}

	_, err := pc.service.ForgotPassword(req.Email)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"message": "If that email exists, a reset link has been sent"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "If that email exists, a reset link has been sent"})
}

func (pc *ProfileController) ResetPassword(c *gin.Context) {
	var req dto.SetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	msg, err := pc.service.ResetPassword(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": msg})
}

func (pc *ProfileController) UpdateProfileForOwner(c *gin.Context) {
	idParam := c.Param("id")
	profileID, err := strconv.ParseUint(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid profile ID"})
		return
	}

	rawEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	email := rawEmail.(string)

	currentProfile, err := pc.service.GetProfileByEmail(email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve current profile"})
		return
	}

	if currentProfile.Role != constants.CompanyOwner {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied: only company owner can update users"})
		return
	}

	targetProfile, err := pc.service.GetProfileById(uint(profileID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Target profile not found"})
		return
	}

	if targetProfile.CompanyID != currentProfile.CompanyID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can update only users in your company"})
		return
	}

	var input dto.UpdateProfileDto
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	if input.Password != nil {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*input.Password), bcrypt.DefaultCost)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
			return
		}
		hashed := string(hashedPassword)
		input.Password = &hashed
	}

	err = pc.service.UpdateProfile(uint(profileID), &input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User profile updated successfully"})
}
