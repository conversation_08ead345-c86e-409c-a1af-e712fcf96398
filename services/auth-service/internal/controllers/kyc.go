package controllers

import (
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/middleware"
	"auth-service/internal/models"
	"auth-service/internal/services"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"strconv"
	"time"
)

type KYCController struct {
	service        services.VerificationCompanyServiceInterface
	userService    services.UserServiceInterface
	profileService services.ProfileServiceInterface
}

func NewKYCController(service services.VerificationCompanyServiceInterface, userService services.UserServiceInterface, profileService services.ProfileServiceInterface) *KYCController {
	return &KYCController{
		service:        service,
		profileService: profileService,
		userService:    userService,
	}
}

func (kc *KYCController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	profileOwner := g.Group("", middleware.ProfileMiddleware(kc.profileService, string(constants.CompanyOwner)))
	adminRoute := g.Group("", middleware.Authentication(kc.userService, constants.AdminRole))
	profileOwner.POST("/verify/:company_id", kc.SubmitKYC)
	profileOwner.DELETE("/delete", kc.DeleteKYC)
	adminRoute.PATCH("/:id/status", kc.UpdateStatus)

	return g
}

func (kc *KYCController) SubmitKYC(ctx *gin.Context) {
	companyID, err := strconv.Atoi(ctx.Param("company_id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid company id"})
		return
	}

	var req dto.KYCCreateRequestDto
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	fmt.Println("/////////////////////////////////////")
	fmt.Println(req.Representative.Email)
	fmt.Println(req.Representative.LastName)
	fmt.Println(req.Representative.FirstName)
	fmt.Println(req.Representative.ContactNumber)
	fmt.Println(req.Representative.CompanyRole)
	form, err := ctx.MultipartForm()
	var docs []models.KYCDocuments
	var savedFiles []string

	if err == nil && form != nil {
		if files, exists := form.File["documents"]; exists && len(files) > 0 {
			uploadPath := fmt.Sprintf("uploads/kyc/documents/%d", companyID)
			_ = os.MkdirAll(uploadPath, os.ModePerm)

			for _, file := range files {
				fullPath := path.Join(uploadPath, file.Filename)
				if err := ctx.SaveUploadedFile(file, fullPath); err != nil {
					for _, p := range savedFiles {
						os.Remove(p)
					}
					ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				savedFiles = append(savedFiles, fullPath)
				docs = append(docs, models.KYCDocuments{
					DocumentURL: fullPath,
					UploadedAt:  time.Now(),
				})
			}
		}
	}

	docsSlice := make([]*dto.KYCDocumentDto, len(docs))
	for i, d := range docs {
		docsSlice[i] = &dto.KYCDocumentDto{DocumentURL: d.DocumentURL}
	}
	req.CompanyDetails.Documents = docsSlice

	if err := kc.service.SubmitKYC(uint(companyID), req); err != nil {
		for _, p := range savedFiles {
			os.Remove(p)
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusAccepted)
}

func (kc *KYCController) DeleteKYC(ctx *gin.Context) {
	role, _ := ctx.Get("role")
	if role != "owner" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "only owner can delete kyc"})
		return
	}

	profileRaw, exists := ctx.Get("profile")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "profile not found"})
		return
	}
	profile := profileRaw.(*models.Profile)

	if profile.Company.KYC == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "no KYC record found"})
		return
	}

	kycID := profile.Company.KYC.ID
	if err := kc.service.DeleteKYC(kycID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (kc *KYCController) UpdateStatus(ctx *gin.Context) {
	kycID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid KYC ID"})
		return
	}

	var req struct {
		Status          constants.VerificationStatus `json:"status"`
		RejectionReason string                       `json:"rejection_reason,omitempty"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if !req.Status.IsValid() {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid status"})
		return
	}

	if err := kc.service.UpdateKYCStatus(uint(kycID), req.Status, req.RejectionReason); err != nil {
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}
