package controllers

import (
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/middleware"
	"auth-service/internal/models"
	"auth-service/internal/services"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"strconv"
	"time"
)

type KYBController struct {
	service        services.VerificationCompanyServiceInterface
	userService    services.UserServiceInterface
	profileService services.ProfileServiceInterface
}

func NewKYBController(service services.VerificationCompanyServiceInterface, userService services.UserServiceInterface, profileService services.ProfileServiceInterface) *KYBController {
	return &KYBController{
		service:        service,
		userService:    userService,
		profileService: profileService,
	}
}

func (kc *KYBController) Register(r *gin.RouterGroup, s string) *gin.RouterGroup {
	g := r.Group(s)
	profileOwner := g.Group("", middleware.ProfileMiddleware(kc.profileService, string(constants.CompanyOwner)))
	adminRoute := g.Group("", middleware.Authentication(kc.userService, constants.AdminRole))

	profileOwner.POST("/verify/:company_id", kc.SubmitKYB)
	profileOwner.DELETE("/delete", kc.DeleteKYB)
	adminRoute.PATCH("/:id/status", kc.UpdateStatus)
	return g
}

func (kc *KYBController) SubmitKYB(ctx *gin.Context) {
	companyID, err := strconv.Atoi(ctx.Param("company_id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid company id"})
		return
	}

	var req dto.KYBRequestDto
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	form, err := ctx.MultipartForm()
	var docs []models.AdditionalCompanyDocuments
	var savedFiles []string

	if err == nil && form != nil {
		if files, exists := form.File["documents"]; exists && len(files) > 0 {
			uploadPath := fmt.Sprintf("uploads/kyb/documents/%d", companyID)
			_ = os.MkdirAll(uploadPath, os.ModePerm)

			for _, file := range files {
				fullPath := path.Join(uploadPath, file.Filename)
				if err := ctx.SaveUploadedFile(file, fullPath); err != nil {
					for _, p := range savedFiles {
						os.Remove(p)
					}
					ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				savedFiles = append(savedFiles, fullPath)
				docs = append(docs, models.AdditionalCompanyDocuments{
					DocumentURL: fullPath,
					UploadedAt:  time.Now(),
				})
			}
		}
	}

	req.AdditionalCompanyDocuments = make([]*dto.AdditionalCompanyDocuments, len(docs))
	for i, d := range docs {
		req.AdditionalCompanyDocuments[i] = &dto.AdditionalCompanyDocuments{DocumentURL: d.DocumentURL}
	}

	if err := kc.service.SubmitKYB(uint(companyID), req); err != nil {
		for _, p := range savedFiles {
			os.Remove(p)
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusAccepted)
}

func (kc *KYBController) DeleteKYB(ctx *gin.Context) {
	role, _ := ctx.Get("role")
	if role != "owner" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "only owner can delete kyb"})
		return
	}

	profileRaw, exists := ctx.Get("profile")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "profile not found"})
		return
	}
	profile := profileRaw.(*models.Profile)

	if profile.Company.Kyb == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "no KYB record found"})
		return
	}

	kybID := profile.Company.Kyb.ID
	if err := kc.service.DeleteKYB(kybID); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (kc *KYBController) UpdateStatus(ctx *gin.Context) {
	kybID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid KYB ID"})
		return
	}

	var req struct {
		Status          constants.VerificationStatus `json:"status"`
		RejectionReason string                       `json:"rejection_reason,omitempty"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if !req.Status.IsValid() {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid status"})
		return
	}

	if err := kc.service.UpdateKYBStatus(uint(kybID), req.Status, req.RejectionReason); err != nil {
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}
