package auth

import (
	"context"
	"fmt"
	ssov1 "github.com/Baiel1922/protos/gen/go/sso"
	"google.golang.org/grpc"
)

type authApp struct {
	ssov1.UnimplementedAuthServer
}

func RegisterServerAPI(gRPC *grpc.Server) {
	ssov1.RegisterAuthServer(gRPC, &authApp{})
}

func (s *authApp) Login(ctx context.Context, req *ssov1.LoginRequest) (*ssov1.LoginResponse, error) {
	fmt.Println("hello/////////////////////")
	return &ssov1.LoginResponse{Token: "token 123"}, nil
}

func (s *authApp) Register(
	ctx context.Context,
	in *ssov1.RegisterRequest,
) (*ssov1.RegisterResponse, error) {
	return &ssov1.RegisterResponse{UserId: 23}, nil
}

func (s *authApp) IsAdmin(
	ctx context.Context,
	in *ssov1.IsAdminRequest,
) (*ssov1.IsAdminResponse, error) {
	return &ssov1.IsAdminResponse{IsAdmin: true}, nil
}

func (s *authApp) Logout(ctx context.Context, in *ssov1.LogoutRequest) (*ssov1.LogoutResponse, error) {
	return &ssov1.LogoutResponse{Success: true}, nil
}
