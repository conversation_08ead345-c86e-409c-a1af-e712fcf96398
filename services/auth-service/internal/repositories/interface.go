package repositories

import (
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type Repository struct {
	CacheRepositoryInterface
	UserRepositoryInterface
	CompanyRepositoryInterface
	KYCRepositoryInterface
	KYBRepositoryInterface
	ProfileRepositoryInterface
}

func NewRepository(db *gorm.DB, rd *redis.Client) *Repository {
	return &Repository{
		CacheRepositoryInterface:   NewCacheRepository(rd),
		UserRepositoryInterface:    NewUserRepository(db, rd),
		CompanyRepositoryInterface: NewCompanyRepository(db),
		KYCRepositoryInterface:     NewKYCRepository(db),
		ProfileRepositoryInterface: NewProfileRepository(db),
		KYBRepositoryInterface:     NewKYBRepository(db),
	}
}
