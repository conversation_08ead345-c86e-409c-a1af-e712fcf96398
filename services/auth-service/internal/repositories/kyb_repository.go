package repositories

import (
	"auth-service/internal/constants"
	"auth-service/internal/models"
	"gorm.io/gorm"
)

type KYBRepositoryInterface interface {
	CreateKYB(kybData *models.Kyb) error
	GetKYBByUserID(userID uint) (*models.Kyb, error)
	UpdateKYBStatus(kybID uint, status constants.VerificationStatus) error
	GetKYBByCompanyID(companyID uint) (*models.Kyb, error)
	GetKYBById(kybID uint) (*models.Kyb, error)
	DeleteKYB(kybID uint) error

	CreateBusinessDetails(details *models.BusinessDetails) error
	CreateFinancialDetails(details *models.FinancialDetails) error
	CreateAdditionalCompanyDocuments(tx *gorm.DB, docs []models.AdditionalCompanyDocuments) error

	Begin() *gorm.DB
	Rollback(tx *gorm.DB) error
	Commit(tx *gorm.DB) error
}

type KYBRepository struct {
	db *gorm.DB
}

func NewKYBRepository(db *gorm.DB) *KYBRepository {
	return &KYBRepository{
		db: db,
	}
}

func (k *KYBRepository) CreateKYB(kybData *models.Kyb) error {
	err := k.db.Create(&kybData).Error
	if err != nil {
		return err
	}
	return nil
}

func (k *KYBRepository) GetKYBById(kybID uint) (*models.Kyb, error) {
	kyb := models.Kyb{}
	if err := k.db.Preload("AdditionalCompanyDocuments").
		Preload("BusinessDetails").
		Preload("FinancialDetails").
		Where("id = ?", kybID).First(&kyb).Error; err != nil {
		return nil, err
	}
	return &kyb, nil
}

func (k *KYBRepository) GetKYBByUserID(userID uint) (*models.Kyb, error) {
	kyb := models.Kyb{}
	if err := k.db.Where("id = ?", userID).Preload("Profile").First(&kyb).Error; err != nil {
		return nil, err
	}

	return &kyb, nil

}

func (k *KYBRepository) GetKYBByCompanyID(companyID uint) (*models.Kyb, error) {
	kyb := models.Kyb{}
	if err := k.db.Where("company_id = ?", companyID).First(&kyb).Error; err != nil {
		return nil, err
	}
	return &kyb, nil
}

func (k *KYBRepository) UpdateKYBStatus(kybID uint, status constants.VerificationStatus) error {
	kyb := models.Kyb{}
	if err := k.db.Where("id = ?", kybID).First(&kyb).Error; err != nil {
		return err
	}
	kyb.Status = status

	return k.db.Save(&kyb).Error
}

func (k *KYBRepository) CreateBusinessDetails(details *models.BusinessDetails) error {
	err := k.db.Create(&details).Error
	if err != nil {
		return err
	}
	return nil
}

func (k *KYBRepository) CreateFinancialDetails(details *models.FinancialDetails) error {
	err := k.db.Create(&details).Error
	if err != nil {
		return err
	}
	return nil
}

func (k *KYBRepository) CreateAdditionalCompanyDocuments(tx *gorm.DB, docs []models.AdditionalCompanyDocuments) error {
	if len(docs) == 0 {
		return nil
	}
	return tx.Create(&docs).Error
}

func (k *KYBRepository) DeleteKYB(kybID uint) error {
	return k.db.Delete(&models.Kyb{}, kybID).Error
}

func (k *KYBRepository) Begin() *gorm.DB {
	return k.db.Begin()
}

func (k *KYBRepository) Rollback(tx *gorm.DB) error {
	return tx.Rollback().Error
}

func (k *KYBRepository) Commit(tx *gorm.DB) error {
	return tx.Commit().Error
}
