package repositories

import (
	"auth-service/internal/constants"
	"context"
	"github.com/redis/go-redis/v9"
	"time"
)

type CacheRepositoryInterface interface {
	DeleteCacheWithKey(key string) error
	GetCache(key string) (*string, error)
	SetCache(key string, data []byte) error
}

type CacheRepository struct {
	rds *redis.Client
}

func NewCacheRepository(rds *redis.Client) *CacheRepository {
	return &CacheRepository{
		rds: rds,
	}
}

func (cr *CacheRepository) DeleteCacheWithKey(key string) error {
	keys, err := cr.rds.Keys(context.Background(), key+"*").Result()
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		if err = cr.rds.Del(context.Background(), keys...).Err(); err != nil {
			return err
		}
	}

	return nil
}

func (cr *CacheRepository) GetCache(key string) (*string, error) {
	cachedData, err := cr.rds.Get(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}

	return &cachedData, nil
}

func (cr *CacheRepository) SetCache(key string, data []byte) error {
	if err := cr.rds.Set(context.Background(), key, data, time.Minute*constants.RedisCacheTTL).Err(); err != nil {
		return err
	}

	return nil
}
