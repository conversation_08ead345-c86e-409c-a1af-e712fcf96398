package repositories

import (
	"auth-service/core/connect"
	"auth-service/internal/constants"
	"auth-service/internal/models"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

type CompanyRepositoryInterface interface {
	CreateCompany(company *models.Company) error
	DeleteCompany(companyID uint) error
	GetCompany(companyID uint) (*models.Company, error)
	UpdateCompany(company *models.Company) error
	CreateCompanyWithOwner(company *models.Company, profile *models.Profile) error
	CheckCompanyByEmail(email string) (bool, error)
	GetCompanyByEmail(email string) (*models.Company, error)
	UpdateVerificationStatus(companyID uint, status constants.VerificationStatus) error
	GetCompanies(status string, page, limit int) ([]*models.Company, int64, error)
	DeleteToken(email string) error
}

type CompanyRepository struct {
	db *gorm.DB
}

func NewCompanyRepository(db *gorm.DB) *CompanyRepository {
	return &CompanyRepository{
		db: db,
	}
}

func (cr *CompanyRepository) CreateCompany(company *models.Company) error {
	if err := cr.db.Create(&company).Error; err != nil {
		return err
	}
	return nil
}

func (cr *CompanyRepository) DeleteCompany(companyID uint) error {
	company := models.Company{}
	if err := cr.db.Where("id = ?", companyID).Delete(&company).Error; err != nil {
		return err
	}
	return nil
}

func (cr *CompanyRepository) GetCompany(companyID uint) (*models.Company, error) {
	var company models.Company

	err := cr.db.
		Where("id = ?", companyID).
		Preload("KYC").
		Preload("KYC.CompanyDetails").
		Preload("KYC.Representative").
		Preload("KYC.CompanyDetails.Documents").
		Preload("Kyb").
		Preload("Kyb.BusinessDetails").
		Preload("Kyb.FinancialDetails").
		Preload("Kyb.AdditionalCompanyDocuments").
		First(&company).Error

	if err != nil {
		return nil, err
	}

	return &company, nil
}

func (cr *CompanyRepository) GetCompanies(status string, page, limit int) ([]*models.Company, int64, error) {
	var companies []*models.Company
	var total int64

	query := cr.db.Model(&models.Company{})

	if status != "" {
		query = query.Where("verification_status = ?", status)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * limit
	err = query.Offset(offset).Limit(limit).Find(&companies).Error
	if err != nil {
		return nil, 0, err
	}

	return companies, total, nil
}

func (cr *CompanyRepository) UpdateCompany(company *models.Company) error {
	if err := cr.db.Updates(company).Error; err != nil {
		return err
	}
	return nil
}

func (cr *CompanyRepository) CreateCompanyWithOwner(company *models.Company, profile *models.Profile) error {
	return cr.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(company).Error; err != nil {
			return err
		}

		profile.CompanyID = company.ID
		if err := tx.Create(profile).Error; err != nil {
			return err
		}

		return nil
	})
}

func (cr *CompanyRepository) CheckCompanyByEmail(email string) (bool, error) {
	var count int64
	err := cr.db.Model(&models.Company{}).Where("email = ?", email).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (cr *CompanyRepository) GetCompanyByEmail(email string) (*models.Company, error) {
	var company models.Company
	err := cr.db.Where("email = ?", email).First(&company).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("company with email %s not found", email)
		}
		return nil, fmt.Errorf("failed to get company by email: %w", err)
	}
	return &company, nil
}

func (cr *CompanyRepository) UpdateVerificationStatus(companyID uint, status constants.VerificationStatus) error {
	return cr.db.Model(&models.Company{}).Where("id = ?", companyID).Update("verification_status", status).Error
}

func (cr *CompanyRepository) DeleteToken(email string) error {
	key := fmt.Sprintf("refresh_token:%s", email)
	return connect.Redis.Del(ctx, key).Err()
}
