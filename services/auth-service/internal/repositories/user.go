package repositories

import (
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/models"
	"context"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type UserRepositoryInterface interface {
	BaseRepositoryInterface
	GetAllUsers(pag *dto.PaginationDto, excludedRoles ...string) ([]models.User, int64, error)
	FindUserByUsername(user *models.User, username string) error
	SetToken(username, token string) error
	GetToken(username string) (string, error)
	DeleteToken(username string) error
	FindById(user *models.User, id uint,tx *gorm.DB) error
	UpdateUser(user *models.User) error
	CreateUser(user *models.User) error
	GetAllAdmins() ([]models.User, error)
	DeleteUser(userID uint, tx *gorm.DB) error
}

var ctx = context.Background()

type UserRepository struct {
	BaseRepository
	rds *redis.Client
}

func NewUserRepository(db *gorm.DB, rds *redis.Client) *UserRepository {
	return &UserRepository{
		BaseRepository: BaseRepository{db: db},
		rds: rds,
	}
}

func (ur *UserRepository) DeleteUser(userID uint,tx *gorm.DB) error {
	if err := ur.GetDB(tx).Delete(&models.User{}, userID).Error; err != nil {
		return err
	}
	return nil
}

func (ur *UserRepository) GetAllUsers(pag *dto.PaginationDto, excludedRoles ...string) ([]models.User, int64, error) {
	var users []models.User
	if err := ur.db.Not("role in ?", excludedRoles).
		Order("created_at DESC").
		Offset((pag.Page - 1) * pag.Limit).
		Limit(pag.Limit).
		Find(&users).Error; err != nil {
		return nil, 0, err
	}
	var count int64
	if err := ur.db.Model(&models.User{}).Not("role in ?", excludedRoles).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	return users, count, nil
}

func (ur *UserRepository) GetAllAdmins() ([]models.User, error) {
	var admins []models.User
	if err := ur.db.Where("role = ? OR role = ?", constants.AdminRole, constants.SuperAdmin).Find(&admins).Error; err != nil {
		return nil, err
	}
	return admins, nil
}

func (ur *UserRepository) FindUserByUsername(user *models.User, username string) error {
	if err := ur.db.Where("username = ?", username).First(&user).Error; err != nil {
		return err
	}
	return nil
}

func (ur *UserRepository) FindById(user *models.User, id uint, tx *gorm.DB) error {
	if err := ur.GetDB(tx).Where("id = ?", id).First(user).Error; err != nil {
		return err
	}
	return nil
}

func (ur *UserRepository) CreateUser(user *models.User) error {
	if err := ur.db.Create(&user).Error; err != nil {
		return err
	}
	return nil
}

func (ur *UserRepository) UpdateUser(user *models.User) error {
	if err := ur.db.Updates(user).Error; err != nil {
		return err
	}
	return nil
}

func (ur *UserRepository) SetToken(username, token string) error {
	return ur.rds.Set(ctx, username, token, 0).Err()
}

func (ur *UserRepository) GetToken(username string) (string, error) {
	return ur.rds.Get(ctx, username).Result()
}

func (ur *UserRepository) DeleteToken(username string) error {
	return ur.rds.Del(ctx, username).Err()
}
