package repositories

import (
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/models"
	"errors"
	"gorm.io/gorm"
)

type ProfileRepositoryInterface interface {
	GetProfileByEmail(email string) (*models.Profile, error)
	GetProfileById(id uint) (*models.Profile, error)
	UpdateProfile(profileId uint, input *dto.UpdateProfileDto) error
	CreateProfile(profile *models.Profile) error
	GetPermissionByName(name string) (*models.Permission, error)
	AssignPermissions(profileID uint, permissions []models.Permission) error
	CreatePermission(p *models.Permission) error
	GetPermissionsByProfileID(profileID uint) ([]models.AccountPermission, error)
	GetByCompanyID(companyID uint) (*models.Profile, error)
	CreateAccountPermission(perm *models.AccountPermission) error
	GetAccountPermissions(profileID uint) ([]models.AccountPermission, error)
	Transaction(fn func(txRepo ProfileRepositoryInterface) error) error
	GetAllOwners(limit, offset int) ([]models.Profile, error)
	GetAllProfilesByCompanyId(companyID uint) ([]models.Profile, error)
	UpdateFields(id uint, updates map[string]interface{}) error
}

type ProfileRepository struct {
	db *gorm.DB
}

func NewProfileRepository(db *gorm.DB) *ProfileRepository {
	return &ProfileRepository{
		db: db,
	}
}

func (pr *ProfileRepository) GetProfileByEmail(email string) (*models.Profile, error) {
	profile := models.Profile{}
	if err := pr.db.Where("email = ?", email).Preload("Company").Preload("Company.KYC").
		Preload("Company.Kyb").First(&profile).Error; err != nil {
		return nil, err
	}
	return &profile, nil
}

func (pr *ProfileRepository) GetProfileById(id uint) (*models.Profile, error) {
	profile := models.Profile{}
	if err := pr.db.Where("id = ?", id).Preload("Company").Preload("Company.KYC").
		Preload("Company.Kyb").First(&profile).Error; err != nil {
		return nil, err
	}

	return &profile, nil
}

func (pr *ProfileRepository) CreateProfile(profile *models.Profile) error {
	return pr.db.Create(profile).Error
}

func (pr *ProfileRepository) UpdateProfile(profileId uint, input *dto.UpdateProfileDto) error {
	updates := map[string]interface{}{}

	if input.Name != nil {
		updates["name"] = *input.Name
	}
	if input.Email != nil {
		updates["email"] = *input.Email
	}
	if input.Address != nil {
		updates["address"] = *input.Address
	}
	if input.PhoneNumber != nil {
		updates["phone_number"] = *input.PhoneNumber
	}
	if input.DateOfBirth != nil {
		updates["date_of_birth"] = (*input.DateOfBirth).ToTime()
	}

	if len(updates) == 0 {
		return errors.New("no data was provided")
	}

	result := pr.db.Model(&models.Profile{}).Where("id = ?", profileId).Updates(updates)

	if result.RowsAffected == 0 {
		return errors.New("profile not found")
	}

	return nil
}

func (pr *ProfileRepository) GetPermissionByName(name string) (*models.Permission, error) {
	var perm models.Permission
	if err := pr.db.Where("name = ?", name).First(&perm).Error; err != nil {
		return nil, err
	}
	return &perm, nil
}

func (pr *ProfileRepository) AssignPermissions(profileID uint, permissions []models.Permission) error {
	profile := models.Profile{Entity: models.Entity{ID: profileID}}
	if err := pr.db.First(&profile).Error; err != nil {
		return err
	}

	return pr.db.Model(&profile).Association("Permissions").Replace(&permissions)
}

func (pr *ProfileRepository) CreatePermission(p *models.Permission) error {
	return pr.db.Create(p).Error
}

func (pr *ProfileRepository) GetPermissionsByProfileID(profileID uint) ([]models.AccountPermission, error) {
	var profile models.Profile
	if err := pr.db.Preload("AccountPermissions").First(&profile, profileID).Error; err != nil {
		return nil, err
	}
	return profile.AccountPermissions, nil
}

func (pr *ProfileRepository) GetByCompanyID(companyID uint) (*models.Profile, error) {
	var profile models.Profile
	err := pr.db.
		Where("company_id = ?", companyID).
		First(&profile).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

func (pr *ProfileRepository) CreateAccountPermission(perm *models.AccountPermission) error {
	return pr.db.Create(perm).Error
}

func (pr *ProfileRepository) GetAccountPermissions(profileID uint) ([]models.AccountPermission, error) {
	var permissions []models.AccountPermission
	err := pr.db.Where("profile_id = ?", profileID).Find(&permissions).Error
	return permissions, err
}

func (pr *ProfileRepository) Transaction(fn func(txRepo ProfileRepositoryInterface) error) error {
	return pr.db.Transaction(func(tx *gorm.DB) error {
		txRepo := NewProfileRepository(tx)
		return fn(txRepo)
	})
}

func (pr *ProfileRepository) GetAllOwners(limit, offset int) ([]models.Profile, error) {
	var owners []models.Profile
	err := pr.db.
		Where("role = ?", constants.CompanyOwner).
		Preload("Company").
		Limit(limit).
		Offset(offset).
		Find(&owners).Error
	if err != nil {
		return nil, err
	}
	return owners, nil
}

func (pr *ProfileRepository) GetAllProfilesByCompanyId(companyID uint) ([]models.Profile, error) {
	var profiles []models.Profile
	err := pr.db.
		Where("company_id = ?", companyID).
		Preload("AccountPermissions").
		Find(&profiles).Error
	return profiles, err
}

func (pr *ProfileRepository) UpdateFields(id uint, updates map[string]interface{}) error {
	return pr.db.Model(&models.Profile{}).Where("id = ?", id).Updates(updates).Error
}
