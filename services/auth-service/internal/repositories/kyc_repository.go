package repositories

import (
	"auth-service/internal/constants"
	"auth-service/internal/models"
	"gorm.io/gorm"
)

type KYCRepositoryInterface interface {
	CreateKYC(kycData *models.KYC) error
	GetKYCByUserID(userID uint) (*models.KYC, error)
	UpdateKYCStatus(kycID uint, status constants.VerificationStatus) error
	GetKYCByCompanyID(companyID uint) (*models.KYC, error)
	GetKYCById(kycID uint) (*models.KYC, error)
	DeleteKYC(kybID uint) error

	// CreateCompanyDetails CompanyDetails, Representative and Documents
	CreateCompanyDetails(details *models.CompanyDetails) error
	CreateRepresentative(representative *models.Representative) error
	AddKYCDocuments(documents *[]models.KYCDocuments) error
	GetCompanyDetailsByID(detailsID uint) (*models.CompanyDetails, error)

	//transactions

	Begin() *gorm.DB
	Rollback(tx *gorm.DB) error
	Commit(tx *gorm.DB) error
}

type KYCRepository struct {
	db *gorm.DB
}

func NewKYCRepository(db *gorm.DB) *KYCRepository {
	return &KYCRepository{
		db: db,
	}
}

func (k *KYCRepository) CreateKYC(kycData *models.KYC) error {
	err := k.db.Create(&kycData).Error
	if err != nil {
		return err
	}
	return nil
}

func (k *KYCRepository) GetKYCByUserID(userID uint) (*models.KYC, error) {
	kyc := models.KYC{}
	if err := k.db.Where("id = ?", userID).Preload("Profile").First(&kyc).Error; err != nil {
		return nil, err
	}

	return &kyc, nil

}
func (k *KYCRepository) GetKYCByCompanyID(companyID uint) (*models.KYC, error) {
	kyc := models.KYC{}
	if err := k.db.Where("company_id = ?", companyID).Preload("Company").First(&kyc).Error; err != nil {
		return nil, err
	}
	return &kyc, nil
}

func (k *KYCRepository) UpdateKYCStatus(kycID uint, status constants.VerificationStatus) error {
	kyc := models.KYC{}
	if err := k.db.Where("id = ?", kycID).First(&kyc).Error; err != nil {
		return err
	}
	kyc.Status = status
	return k.db.Save(&kyc).Error
}

func (k *KYCRepository) CreateCompanyDetails(details *models.CompanyDetails) error {
	err := k.db.Create(&details).Error
	if err != nil {
		return err
	}
	return nil
}

func (k *KYCRepository) CreateRepresentative(representative *models.Representative) error {
	err := k.db.Create(&representative).Error
	if err != nil {
		return err
	}
	return nil
}

func (k *KYCRepository) GetKYCById(kycID uint) (*models.KYC, error) {
	kyc := models.KYC{}
	if err := k.db.Preload("CompanyDetails.Documents").
		Preload("Representative").
		Where("id = ?", kycID).First(&kyc).Error; err != nil {
		return nil, err
	}
	return &kyc, nil
}

func (k *KYCRepository) GetCompanyDetailsByID(detailsID uint) (*models.CompanyDetails, error) {
	details := models.CompanyDetails{}
	if err := k.db.Preload("Documents").Where("id = ?", detailsID).First(&details).Error; err != nil {
		return nil, err
	}
	return &details, nil
}

func (k *KYCRepository) DeleteKYC(kybID uint) error {
	return k.db.Delete(&models.KYC{}, kybID).Error
}

func (k *KYCRepository) AddKYCDocuments(documents *[]models.KYCDocuments) error {
	return nil
}

func (k *KYCRepository) Begin() *gorm.DB {
	return k.db.Begin()
}

func (k *KYCRepository) Rollback(tx *gorm.DB) error {
	return tx.Rollback().Error
}

func (k *KYCRepository) Commit(tx *gorm.DB) error {
	return tx.Commit().Error
}
