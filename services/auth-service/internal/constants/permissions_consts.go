package constants

type Permission string

const (
	PermissionAccountView     Permission = "account:view"
	PermissionAccountManage   Permission = "account:manage"
	PermissionPaymentInitiate Permission = "payment:initiate"
	PermissionPaymentApprove  Permission = "payment:approve"
)

var RolePermissions = map[CompanyRole][]Permission{
	CompanyOwner: {
		PermissionAccountView,
		PermissionAccountManage,
		PermissionPaymentInitiate,
		PermissionPaymentApprove,
	},
	AdminRole: {
		PermissionAccountView,
		PermissionAccountManage,
		PermissionPaymentInitiate,
	},
	CompanyStaff: {
		PermissionAccountView,
		PermissionPaymentInitiate,
	},
}
