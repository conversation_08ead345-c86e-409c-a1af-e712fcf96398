package constants

type VerificationStatus string

const (
	StatusPending  VerificationStatus = "pending"
	StatusVerified VerificationStatus = "verified"
	StatusRejected VerificationStatus = "rejected"
	StatusInReview VerificationStatus = "in_review"
)

func (s VerificationStatus) IsValid() bool {
	switch s {
	case StatusPending, StatusVerified, StatusRejected, StatusInReview:
		return true
	default:
		return false
	}
}
