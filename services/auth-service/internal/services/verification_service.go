package services

import (
	"auth-service/core/connect"
	"auth-service/internal/constants"
	"auth-service/internal/dto"
	"auth-service/internal/models"
	"auth-service/internal/repositories"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"os"
)

type VerificationCompanyServiceInterface interface {
	UpdateKYBStatus(kybID uint, status constants.VerificationStatus, rejectionReason string) error
	UpdateKYCStatus(kycID uint, status constants.VerificationStatus, rejectionReason string) error
	EvaluateCompanyStatus(companyID uint) error
	SubmitKYB(companyID uint, kybRequest dto.KYBRequestDto) error
	SubmitKYC(companyID uint, kycRequest dto.KYCCreateRequestDto) error
	DeleteKYB(kybID uint) error
	DeleteKYC(kycID uint) error
	GetKycByCompanyID(companyID uint) (*models.KYC, error)
	GetKybByCompanyID(companyID uint) (*models.Kyb, error)
}

type VerificationCompanyService struct {
	kybRepo     repositories.KYBRepositoryInterface
	kycRepo     repositories.KYCRepositoryInterface
	companyRepo repositories.CompanyRepositoryInterface
	profileRepo repositories.ProfileRepositoryInterface
}

func NewVerificationCompanyService(kybRepo repositories.KYBRepositoryInterface, kycRepo repositories.KYCRepositoryInterface, companyRepo repositories.CompanyRepositoryInterface, profileRepo repositories.ProfileRepositoryInterface) *VerificationCompanyService {
	return &VerificationCompanyService{
		kybRepo:     kybRepo,
		kycRepo:     kycRepo,
		companyRepo: companyRepo,
		profileRepo: profileRepo,
	}
}

func (s *VerificationCompanyService) SubmitKYB(companyID uint, kybRequest dto.KYBRequestDto) error {
	tx := s.kybRepo.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	profile, err := s.profileRepo.GetByCompanyID(companyID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get profile by ID: %w", err)
	}

	existingKYB, err := s.kybRepo.GetKYBByCompanyID(companyID)
	if err == nil && existingKYB != nil {
		return fmt.Errorf("KYC already exists for this company")
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		tx.Rollback()
		return fmt.Errorf("failed to check existing KYC: %w", err)
	}

	var documents []models.AdditionalCompanyDocuments
	if kybRequest.AdditionalCompanyDocuments != nil {
		for _, path := range kybRequest.AdditionalCompanyDocuments {
			documents = append(documents, models.AdditionalCompanyDocuments{
				DocumentURL: path.DocumentURL,
				UploadedAt:  kybRequest.BusinessDetails.Date,
			})
		}
	}

	businessDetails := models.BusinessDetails{
		BusinessAddress:    kybRequest.BusinessDetails.BusinessAddress,
		CompanyTradingName: kybRequest.BusinessDetails.CompanyTradingName,
		BusinessActivity:   kybRequest.BusinessDetails.BusinessActivity,
		BusinessPartners:   kybRequest.BusinessDetails.BusinessPartners,
		BusinessWebsite:    kybRequest.BusinessDetails.BusinessWebsite,
		BusinessLocation:   kybRequest.BusinessDetails.BusinessLocation,
	}

	if err := s.kybRepo.CreateBusinessDetails(&businessDetails); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create kyb business details: %w", err)
	}

	financialDetails := models.FinancialDetails{
		Purpose:              kybRequest.FinancialDetails.Purpose,
		Sectors:              kybRequest.FinancialDetails.Sectors,
		PaymentFromCountries: kybRequest.FinancialDetails.PaymentFromCountries,
		PaymentToCountries:   kybRequest.FinancialDetails.PaymentToCountries,
		IncomeEUR:            kybRequest.FinancialDetails.IncomeEUR,
		TxCount:              kybRequest.FinancialDetails.TxCount,
		AvgTxAmount:          kybRequest.FinancialDetails.AvgTxAmount,
		OutgoingTxCount:      kybRequest.FinancialDetails.OutgoingTxCount,
		AvgOutgoingTxAmount:  kybRequest.FinancialDetails.AvgOutgoingTxAmount,
	}

	if err := s.kybRepo.CreateFinancialDetails(&financialDetails); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create kyb financial_details: %w", err)

	}

	kyc := models.Kyb{
		CompanyID:                  companyID,
		FinancialDetailsID:         financialDetails.ID,
		BusinessDetailsID:          businessDetails.ID,
		AdditionalCompanyDocuments: documents,
		Status:                     constants.StatusPending,
	}

	if err := s.kybRepo.CreateKYB(&kyc); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create company kyc data: %w", err)

	}

	notification := dto.NotificationPayload{
		UserId:  profile.ID,
		Type:    constants.NotificationKybAdded,
		Message: "new kyb application added",
		Role:    string(constants.CompanyOwner),
	}
	err = connect.RabbitMQ.Publish(
		constants.NotificationsExchange,
		constants.NotificationKybAdded,
		notification,
	)
	if err != nil {
		return err
	}

	return nil
}

func (s *VerificationCompanyService) SubmitKYC(companyID uint, kycRequest dto.KYCCreateRequestDto) error {
	tx := s.kycRepo.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	profile, err := s.profileRepo.GetByCompanyID(companyID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get profile by ID: %w", err)
	}

	existingKYC, err := s.kycRepo.GetKYCByCompanyID(companyID)
	if err == nil && existingKYC != nil {
		return fmt.Errorf("KYC already exists for this company")
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		tx.Rollback()
		return fmt.Errorf("failed to check existing KYC: %w", err)
	}

	// Преобразуем []string в []models.KYCDocuments
	var documents []models.KYCDocuments
	if kycRequest.CompanyDetails.Documents != nil {
		for _, path := range kycRequest.CompanyDetails.Documents {
			documents = append(documents, models.KYCDocuments{
				DocumentURL: path.DocumentURL,
				UploadedAt:  kycRequest.CompanyDetails.Date,
			})
		}
	}

	companyDetails := models.CompanyDetails{
		CompanyName:        kycRequest.CompanyDetails.CompanyName,
		Country:            kycRequest.CompanyDetails.Country,
		RegistrationNumber: kycRequest.CompanyDetails.RegistrationNumber,
		TypeOfEntity:       kycRequest.CompanyDetails.TypeOfEntity,
		LegalAddress:       kycRequest.CompanyDetails.LegalAddress,
		City:               kycRequest.CompanyDetails.City,
		Website:            kycRequest.CompanyDetails.Website,
		CompanyEmail:       kycRequest.CompanyDetails.CompanyEmail,
		CompanyPhone:       kycRequest.CompanyDetails.CompanyPhone,
		Date:               kycRequest.CompanyDetails.Date,
		Documents:          documents,
	}

	if err := s.kycRepo.CreateCompanyDetails(&companyDetails); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create company details: %w", err)
	}

	representativeData := models.Representative{
		FirstName:     kycRequest.Representative.FirstName,
		LastName:      kycRequest.Representative.LastName,
		Email:         kycRequest.Representative.Email,
		ContactNumber: kycRequest.Representative.ContactNumber,
		CompanyRole:   kycRequest.Representative.CompanyRole,
	}

	if err := s.kycRepo.CreateRepresentative(&representativeData); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create company representative: %w", err)

	}

	kyc := models.KYC{
		CompanyID:        companyID,
		CompanyDetailsID: companyDetails.ID,
		RepresentativeID: representativeData.ID,
		Status:           constants.StatusPending,
	}

	if err := s.kycRepo.CreateKYC(&kyc); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create company kyc data: %w", err)

	}

	notification := dto.NotificationPayload{
		UserId:  profile.ID,
		Type:    constants.NotificationKycAdded,
		Message: "new kyc application added",
		Role:    string(constants.CompanyOwner),
	}
	err = connect.RabbitMQ.Publish(
		constants.NotificationsExchange,
		constants.NotificationKycAdded,
		notification,
	)
	if err != nil {
		return err
	}

	return s.kycRepo.Commit(tx)

}

func (s *VerificationCompanyService) UpdateKYBStatus(kybID uint, status constants.VerificationStatus, rejectionReason string) error {

	kyb, err := s.kybRepo.GetKYBById(kybID)
	if err != nil {
		return err
	}

	profile, err := s.profileRepo.GetByCompanyID(kyb.CompanyID)
	if err != nil {
		return fmt.Errorf("failed to get profile by ID: %w", err)
	}

	switch status {
	case constants.StatusRejected:
		err := s.sendNotification(profile.ID, constants.NotificationKybRejected, rejectionReason)
		if err != nil {
			return err
		}
		if err := s.DeleteKYB(kybID); err != nil {
			return fmt.Errorf("failed to delete rejected KYB: %w", err)
		}
		return nil

	case constants.StatusVerified:
		err := s.sendNotification(profile.ID, constants.NotificationKybVerified, "KYB verified")
		if err != nil {
			return err
		}
	}

	err = s.kybRepo.UpdateKYBStatus(kybID, status)
	if err != nil {
		return err
	}

	return s.EvaluateCompanyStatus(kyb.CompanyID)
}

func (s *VerificationCompanyService) UpdateKYCStatus(kycID uint, status constants.VerificationStatus, rejectionReason string) error {
	kyc, err := s.kycRepo.GetKYCById(kycID)
	if err != nil {
		return err
	}

	profile, err := s.profileRepo.GetByCompanyID(kyc.CompanyID)
	if err != nil {
		return fmt.Errorf("failed to get profile by ID: %w", err)
	}

	switch status {
	case constants.StatusRejected:
		err := s.sendNotification(profile.ID, constants.NotificationKycRejected, rejectionReason)
		if err != nil {
			return err
		}
		if err := s.DeleteKYC(kycID); err != nil {
			return fmt.Errorf("failed to delete rejected KYC: %w", err)
		}
		return nil

	case constants.StatusVerified:
		err := s.sendNotification(profile.ID, constants.NotificationKycVerified, "KYC verified")
		if err != nil {
			return err
		}
	}

	err = s.kycRepo.UpdateKYCStatus(kycID, status)
	if err != nil {
		return err
	}

	return s.EvaluateCompanyStatus(kyc.CompanyID)
}

func (s *VerificationCompanyService) EvaluateCompanyStatus(companyID uint) error {
	kyb, _ := s.kybRepo.GetKYBByCompanyID(companyID)
	kyc, _ := s.kycRepo.GetKYCByCompanyID(companyID)

	newStatus := constants.StatusPending

	if (kyb != nil && kyb.Status == constants.StatusRejected) ||
		(kyc != nil && kyc.Status == constants.StatusRejected) {
		newStatus = constants.StatusInReview
	} else if kyb != nil && kyc != nil &&
		kyb.Status == constants.StatusVerified &&
		kyc.Status == constants.StatusVerified {
		newStatus = constants.StatusVerified
	} else if kyb != nil && kyc != nil &&
		kyb.Status == constants.StatusPending &&
		kyc.Status == constants.StatusPending {
		newStatus = constants.StatusPending
	} else {
		newStatus = constants.StatusInReview
	}

	return s.companyRepo.UpdateVerificationStatus(companyID, newStatus)
}

func (s *VerificationCompanyService) DeleteKYB(kybID uint) error {
	kyb, err := s.kybRepo.GetKYBById(kybID)
	if err != nil {
		return fmt.Errorf("failed to get KYB: %w", err)
	}

	var documentPaths []string
	for _, doc := range kyb.AdditionalCompanyDocuments {
		documentPaths = append(documentPaths, doc.DocumentURL)
	}

	if err := s.kybRepo.DeleteKYB(kybID); err != nil {
		return fmt.Errorf("failed to delete KYB: %w", err)
	}

	for _, path := range documentPaths {
		if err := os.Remove(path); err != nil {
			log.Printf("Error deleting file %s: %v", path, err)
		}
	}
	return nil
}
func (s *VerificationCompanyService) DeleteKYC(kycID uint) error {
	kyc, err := s.kycRepo.GetKYCById(kycID)
	if err != nil {
		return fmt.Errorf("failed to get KYC: %w", err)
	}

	companyDetails, err := s.kycRepo.GetCompanyDetailsByID(kyc.CompanyDetailsID)
	if err != nil {
		return fmt.Errorf("failed to get company details: %w", err)
	}

	var documentPaths []string
	for _, doc := range companyDetails.Documents {
		documentPaths = append(documentPaths, doc.DocumentURL)
	}

	if err := s.kycRepo.DeleteKYC(kycID); err != nil {
		return fmt.Errorf("failed to delete KYC: %w", err)
	}

	for _, path := range documentPaths {
		if err := os.Remove(path); err != nil {
			log.Printf("Error deleting file %s: %v", path, err)
		}
	}
	return nil
}
func (s *VerificationCompanyService) GetKycByCompanyID(companyID uint) (*models.KYC, error) {
	return s.kycRepo.GetKYCByCompanyID(companyID)
}

func (s *VerificationCompanyService) GetKybByCompanyID(companyID uint) (*models.Kyb, error) {
	return s.kybRepo.GetKYBByCompanyID(companyID)
}

func (s *VerificationCompanyService) sendNotification(userID uint, notifType string, message string) error {
	payload := dto.NotificationPayload{
		UserId:  userID,
		Type:    notifType,
		Message: message,
		Role:    string(constants.CompanyOwner),
	}
	return connect.RabbitMQ.Publish(constants.NotificationsExchange, notifType, payload)
}
