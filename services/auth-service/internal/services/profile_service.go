package services

import (
	"auth-service/core/config"
	"auth-service/core/connect"
	"auth-service/internal/dto"
	"auth-service/internal/models"
	"auth-service/internal/repositories"
	"auth-service/pkg/security"
	"auth-service/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
	"time"
)

type ProfileServiceInterface interface {
	GetProfileByEmail(email string) (*models.Profile, error)
	GetProfileById(id uint) (*models.Profile, error)
	UpdateProfile(id uint, input *dto.UpdateProfileDto) error
	AcceptInvite(input *dto.SetPasswordRequest) (string, string, error)
	GetAllOwners(limit, offset int) ([]models.Profile, error)
	HasAccountPermission(profileID, accountID uint, permission string) bool
	GetAccountPermissions(profileID uint) ([]models.AccountPermission, error)
	ForgotPassword(email string) (string, error)
	ResetPassword(input dto.SetPasswordRequest) (string, error)
}

type ProfileService struct {
	repo repositories.ProfileRepositoryInterface
}

func NewProfileService(repo repositories.ProfileRepositoryInterface) *ProfileService {
	return &ProfileService{
		repo: repo,
	}
}

func (pc *ProfileService) GetProfileByEmail(email string) (*models.Profile, error) {
	profile, err := pc.repo.GetProfileByEmail(email)
	if err != nil {
		return nil, err
	}

	return profile, err
}

func (pc *ProfileService) GetProfileById(id uint) (*models.Profile, error) {
	profile, err := pc.repo.GetProfileById(id)
	if err != nil {
		return nil, err
	}
	return profile, err
}

func (pc *ProfileService) UpdateProfile(id uint, input *dto.UpdateProfileDto) error {
	return pc.repo.UpdateProfile(id, input)
}

func (pc *ProfileService) AcceptInvite(input *dto.SetPasswordRequest) (string, string, error) {
	if input.Password != input.ConfirmPassword {
		return "", "", errors.New("passwords do not match")
	}

	redisKey := fmt.Sprintf("invite:%s", input.Token)
	ctx := context.Background()
	raw, err := connect.Redis.Get(ctx, redisKey).Result()
	if errors.Is(err, redis.Nil) {
		return "", "", errors.New("invalid or expired token")
	}
	if err != nil {
		return "", "", fmt.Errorf("redis error: %v", err)
	}

	var inviteData dto.InviteData
	if err := json.Unmarshal([]byte(raw), &inviteData); err != nil {
		return "", "", fmt.Errorf("failed to parse invite data: %v", err)
	}

	if _, err := pc.repo.GetProfileByEmail(inviteData.Email); err == nil {
		return "", "", errors.New("user with this email already exists")
	}

	hashed, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		return "", "", fmt.Errorf("failed to hash password: %v", err)
	}

	var newProfile models.Profile
	err = pc.repo.Transaction(func(txRepo repositories.ProfileRepositoryInterface) error {
		newProfile = models.Profile{
			CompanyID: inviteData.CompanyID,
			Name:      fmt.Sprintf("%s %s", inviteData.FirstName, inviteData.LastName),
			Email:     inviteData.Email,
			Password:  string(hashed),
			Role:      inviteData.Role,
		}
		if err := txRepo.CreateProfile(&newProfile); err != nil {
			return fmt.Errorf("failed to create profile: %v", err)
		}

		for _, ap := range inviteData.AccountPermissions {
			for _, perm := range ap.Permissions {
				accountPerm := models.AccountPermission{
					ProfileID:  newProfile.ID,
					AccountID:  ap.AccountID,
					Permission: perm,
				}
				if err := txRepo.CreateAccountPermission(&accountPerm); err != nil {
					return fmt.Errorf("failed to create account permission: %v", err)
				}
			}
		}

		return nil
	})
	if err != nil {
		return "", "", err
	}

	if err := connect.Redis.Del(ctx, redisKey).Err(); err != nil {
		fmt.Printf("warn: could not delete redis key %s: %v\n", redisKey, err)
	}

	permMap := make(map[uint][]string)
	for _, ap := range inviteData.AccountPermissions {
		for _, perm := range ap.Permissions {
			permMap[ap.AccountID] = append(permMap[ap.AccountID], perm)
		}
	}

	accessToken, refreshToken, err := security.GenerateTokens(
		newProfile.ID,
		newProfile.Email,
		string(newProfile.Role),
		permMap,
	)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate tokens: %v", err)
	}

	return accessToken, refreshToken, nil
}

func (pc *ProfileService) GetAllOwners(limit, offset int) ([]models.Profile, error) {
	return pc.repo.GetAllOwners(limit, offset)
}

func (pc *ProfileService) HasAccountPermission(profileID, accountID uint, permission string) bool {
	permissions, err := pc.repo.GetAccountPermissions(profileID)
	if err != nil {
		return false
	}

	for _, p := range permissions {
		if p.AccountID == accountID && p.Permission == permission {
			return true
		}
	}
	return false
}

func (pc *ProfileService) GetAccountPermissions(profileID uint) ([]models.AccountPermission, error) {
	return pc.repo.GetAccountPermissions(profileID)
}

func (pc *ProfileService) ForgotPassword(email string) (string, error) {
	profile, err := pc.repo.GetProfileByEmail(email)
	if err != nil {
		return "", errors.New("make sure that mail with this name exists")
	}

	ctx := context.Background()
	conf := config.Get()

	token := uuid.New().String()

	payload, err := json.Marshal(email)
	if err != nil {
		return "", fmt.Errorf("failed to marshal data: %v", err)
	}

	redisKey := fmt.Sprintf("reset_pass:%s", token)
	if err := connect.Redis.Set(ctx, redisKey, payload, 5*time.Minute).Err(); err != nil {
		return "", fmt.Errorf("failed to set reset_pass: %v", err)
	}

	resetPasswordLink := fmt.Sprintf("%s/reset-password?token=%s", conf.Server.FrontUrl, token)

	htmlContext, err := utils.RenderTemplate("invite_email.html", map[string]string{
		"InviteLink": resetPasswordLink,
		"FirstName":  profile.Name,
	})
	if err != nil {
		return "", fmt.Errorf("error while rendering html: %w", err)
	}

	emails := utils.Email{
		SenderName:       conf.SendGrid.SenderName,
		SenderEmail:      conf.SendGrid.SenderEmail,
		RecipientEmail:   email,
		Subject:          "Reset Password",
		HtmlContent:      htmlContext,
		PlainTextContent: fmt.Sprintf("Press the link below \n %s \n to reset_password", resetPasswordLink),
	}

	if err := utils.SendEmail(emails); err != nil {
		return "", fmt.Errorf("error while sending message to email :%v", err)
	}

	return token, nil
}

func (pc *ProfileService) ResetPassword(input dto.SetPasswordRequest) (string, error) {
	ctx := context.Background()

	redisKey := fmt.Sprintf("reset_pass:%s", input.Token)
	raw, err := connect.Redis.Get(ctx, redisKey).Result()
	if errors.Is(err, redis.Nil) {
		return "", errors.New("invalid or expired token")
	}
	if err != nil {
		return "", fmt.Errorf("redis error :%v", err)
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %v", err)
	}

	var email string
	if err := json.Unmarshal([]byte(raw), &email); err != nil {
		return "", fmt.Errorf("failed to parse data: %v", err)
	}

	profile, err := pc.repo.GetProfileByEmail(email)
	if err != nil {
		return "", fmt.Errorf("profile not found for email: %v", err)
	}

	err = pc.repo.Transaction(func(txRepo repositories.ProfileRepositoryInterface) error {
		updates := map[string]interface{}{
			"password": string(hashedPassword),
		}
		if err := txRepo.UpdateFields(profile.ID, updates); err != nil {
			return fmt.Errorf("failed to update password: %v", err)
		}
		return nil
	})
	if err != nil {
		return "", err
	}

	if err := connect.Redis.Del(ctx, redisKey).Err(); err != nil {
		return "", err
	}

	return "Password has been successfully reset", nil
}
