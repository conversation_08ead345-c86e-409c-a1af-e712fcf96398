package services

import (
	"auth-service/core/config"
	"auth-service/internal/repositories"
)

type Service struct {
	UserServiceInterface
	KYCServiceInterface
	KYBServiceInterface
	CompanyServiceInterface
	ProfileServiceInterface
	VerificationCompanyServiceInterface
}

func NewService(repos *repositories.Repository, conf config.Config) *Service {
	return &Service{
		UserServiceInterface:                NewUserService(repos.UserRepositoryInterface),
		CompanyServiceInterface:             NewCompanyService(repos.CompanyRepositoryInterface, repos.ProfileRepositoryInterface),
		ProfileServiceInterface:             NewProfileService(repos.ProfileRepositoryInterface),
		VerificationCompanyServiceInterface: NewVerificationCompanyService(repos.KYBRepositoryInterface, repos.KYCRepositoryInterface, repos.CompanyRepositoryInterface, repos.ProfileRepositoryInterface),
	}
}
