package seeds

import (
	"auth-service/internal/models"
	"auth-service/pkg/security"
	"encoding/json"
)

type UserSeed struct {
	*GenericSeed
}

func NewUserSeed() *UserSeed {
	s := NewGenericSeed(models.User{})
	return &UserSeed{&s}
}

func (s *UserSeed) Seed() (Summary, error) {
	defer s.Summarize()
	data, err := s.LoadFixture()
	if err != nil {
		return s.Error(err)
	}

	var fixtureData map[string][]models.User
	if err = json.Unmarshal(data, &fixtureData); err != nil {
		return s.Error(err)
	}

	for _, user := range fixtureData["data"] {
		var exists bool = s.Exists(map[string]interface{}{
			"username": user.Username,
		})

		if exists {
			s.Summary.Exist++
			continue
		}

		hashPassword, err := security.Hash(user.Password)
		if err != nil {
			s.<PERSON>g<PERSON>ail(err.<PERSON>rror())
			s.Summary.Errors++
			continue
		}

		user.Password = string(hashPassword)

		r := s.Query.Create(&user)
		if err := r.Error; err != nil {
			s.Log<PERSON>ail(err.Error())
			s.Summary.Errors++
			continue
		}

		s.Summary.Created += r.RowsAffected
	}

	return s.Summary, nil
}
