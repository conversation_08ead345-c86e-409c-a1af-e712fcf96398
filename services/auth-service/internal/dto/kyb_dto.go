package dto

import "time"

type AdditionalCompanyDocuments struct {
	DocumentURL string `json:"document_url"`
}

type BusinessDetails struct {
	BusinessAddress    string    `json:"business_address" form:"business_address"`
	CompanyTradingName string    `json:"company_trading_name" form:"company_trading_name"`
	BusinessActivity   string    `json:"business_activity" form:"business_activity"`
	BusinessPartners   string    `json:"business_partners" form:"business_partners"`
	BusinessWebsite    string    `json:"business_website" form:"business_website"`
	BusinessLocation   string    `json:"business_location" form:"business_location"`
	Date               time.Time `json:"date" form:"date" time_format:"2006-01-02"` // Указываем формат даты для поля Date
}

type FinancialDetails struct {
	Purpose              string `json:"purpose" form:"purpose"`                               // Purpose of the payment account
	Sectors              string `json:"sectors" form:"sectors"`                               // Target business sectors
	PaymentFromCountries string `json:"payment_from_countries" form:"payment_from_countries"` // Possibly comma-separated or list
	PaymentToCountries   string `json:"payment_to_countries" form:"payment_to_countries"`     // Possibly comma-separated or list
	IncomeEUR            int    `json:"income_eur" form:"income_eur"`                         // Expected income transactions in EUR
	TxCount              int    `json:"tx_count" form:"tx_count"`                             // Expected transactions number
	AvgTxAmount          int    `json:"avg_tx_amount" form:"avg_tx_amount"`                   // Avg. amount of a single transaction
	OutgoingTxCount      int    `json:"outgoing_tx_count" form:"outgoing_tx_count"`           // Expected outgoing transactions number
	AvgOutgoingTxAmount  int    `json:"avg_outgoing_tx_amount" form:"avg_outgoing_tx_amount"` // Avg. amount of a single outgoing transaction
}

type KYBRequestDto struct {
	BusinessDetails            BusinessDetails               `form:"business_details"`
	FinancialDetails           FinancialDetails              `form:"financial_details"`
	AdditionalCompanyDocuments []*AdditionalCompanyDocuments `form:"additional_company_documents"`
}
