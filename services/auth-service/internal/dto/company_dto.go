package dto

import "auth-service/internal/constants"

type CompanyRequest struct {
	CompanyTradingName string `json:"company_trading_name" binding:"required,min=2"`
	Country            string `json:"country" binding:"required"`
	ContactPhone       string `json:"contact_phone" binding:"required"`
	Email              string `json:"email" binding:"required,email"`
	Password           string `json:"password" binding:"required,min=9"`
	SelectedProduct    string `json:"selected_product" binding:"required"`
}

type InviteData struct {
	CompanyID          uint                       `json:"company_id"`
	FirstName          string                     `json:"first_name"`
	LastName           string                     `json:"last_name"`
	Email              string                     `json:"email"`
	Role               constants.CompanyRole      `json:"role"`
	AccountPermissions []AccountPermissionRequest `json:"account_permissions,omitempty"`
}

type AccountPermissionRequest struct {
	AccountID   uint     `json:"account_id" binding:"required"`
	Permissions []string `json:"permissions" binding:"required"`
}
