package dto

import (
	"time"
)

type KYCDocumentDto struct {
	DocumentURL string `json:"document_url"`
}

type CompanyDetailsDto struct {
	CompanyName        string    `form:"company_name"`
	Country            string    `form:"country"`
	RegistrationNumber string    `form:"registration_number"`
	TypeOfEntity       string    `form:"type_of_entity"`
	LegalAddress       string    `form:"legal_address"`
	City               string    `form:"city"`
	Website            string    `form:"website"`
	CompanyEmail       string    `form:"company_email"`
	CompanyPhone       string    `form:"company_phone"`
	Date               time.Time `form:"date"`
	Documents          []*KYCDocumentDto
}

type RepresentativeDto struct {
	FirstName     string `form:"first_name"`
	LastName      string `form:"last_name"`
	Email         string `form:"email"`
	ContactNumber string `form:"contact_number"`
	CompanyRole   string `form:"company_role"`
}

type KYCCreateRequestDto struct {
	CompanyDetails CompanyDetailsDto `form:""`
	Representative RepresentativeDto `form:""`
}
