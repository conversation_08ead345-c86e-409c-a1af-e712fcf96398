package dto

import (
	"auth-service/pkg/security"
	"errors"
)

type UserInput struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type UsersRespDto struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	Name     string `json:"name"`
	LastName string `json:"last_name"`
	Email    string `json:"email"`
}

type AllUserRespDto struct {
	Count int64
	Users []UsersRespDto
}

type ChangePasswordDto struct {
	OldPassword        string `json:"old_password" binding:"required"`
	NewPassword        string `json:"new_password" binding:"required"`
	NewPasswordConfirm string `json:"new_password_confirm" binding:"required"`
}

func (dto *ChangePasswordDto) Validate(hashedPassword string) error {
	if err := security.VerifyPassword(hashedPassword, dto.OldPassword); err != nil {
		return errors.New("error_invalid_password")
	}
	if dto.NewPassword != dto.NewPasswordConfirm {
		return errors.New("error_passwords_do_not_match")
	}
	return nil
}

type CreateUserDto struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Partner  string `json:"partner"`
	Name     string `json:"name" binding:"required"`
	LastName string `json:"last_name" binding:"required"`
	Email    string `json:"email" binding:"required"`
	Role     string `json:"role" binding:"required"`
}

type UpdateUserDto struct {
	Username *string `json:"username"`
	Name     *string `json:"name"`
	LastName *string `json:"last_name"`
	Email    *string `json:"email"`
	Role     *string `json:"role"`
}

type UserProfileDto struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	LastName string `json:"last_name"`
	Email    string `json:"email"`
	Role     string `json:"role"`
}

//func (dto *CreateUserDto) Validate() error {
//	if dto.Role == "admin" || dto.Role == "operator" || dto.Role == "bot" {
//		return nil
//	}
//	return errors.New("error_permission_denied")
//}

type ResetPasswordDto struct {
	NewPassword        string `binding:"required"`
	NewPasswordConfirm string `binding:"required"`
}

func (dto *ResetPasswordDto) Validate() error {
	if dto.NewPassword != dto.NewPasswordConfirm {
		return errors.New("passwords do not match")
	}
	return nil
}

type AdminDto struct {
	UserId uint
	Role   string
}
