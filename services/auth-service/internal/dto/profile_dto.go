package dto

import (
	"auth-service/internal/constants"
	"auth-service/utils/parsers"
	"time"
)

type ProfileResponseDto struct {
	Name                 string                `json:"name"`
	Email                string                `json:"email"`
	PhoneNumber          string                `json:"phone_number"`
	Address              string                `json:"address"`
	DateOfBirth          time.Time             `json:"date_of_birth"`
	Role                 constants.CompanyRole `gorm:"default:owner" json:"role"`
	CompanyID            uint                  `json:"company_id"`
	VerificationStatuses VerificationStatus    `json:"verification_statuses"`
}

type VerificationStatus struct {
	KYCStatus string `json:"kyc_status"`
	KYBStatus string `json:"kyb_status"`
}

type UpdateProfileDto struct {
	Name        *string                `json:"name,omitempty"`
	Email       *string                `json:"email,omitempty"`
	PhoneNumber *string                `json:"phone_number,omitempty"`
	Address     *string                `json:"address,omitempty"`
	DateOfBirth *parsers.DateOnly      `json:"date_of_birth,omitempty"`
	Role        *constants.CompanyRole `json:"role,omitempty"`
	Password    *string                `json:"password,omitempty"`
}

type SetPasswordRequest struct {
	Token           string `json:"token" binding:"required"`
	Password        string `json:"password" binding:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" binding:"required"`
}

type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type GetAllProfilesResponse struct {
	ID          uint              `json:"id"`
	Name        string            `json:"name"`
	Email       string            `json:"email"`
	PhoneNumber string            `json:"phone_number"`
	Address     string            `json:"address"`
	DateOfBirth time.Time         `json:"date_of_birth"`
	Role        string            `json:"role"`
	Permissions map[uint][]string `json:"permissions"`
}
