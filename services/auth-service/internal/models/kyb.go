package models

import (
	"auth-service/internal/constants"
	"time"
)

type Kyb struct {
	Entity
	CompanyID          uint
	BusinessDetailsID  uint
	FinancialDetailsID uint

	BusinessDetails            BusinessDetails              `gorm:"foreignKey:BusinessDetailsID;constraint:OnDelete:CASCADE;"`
	FinancialDetails           FinancialDetails             `gorm:"foreignKey:FinancialDetailsID;constraint:OnDelete:CASCADE;"`
	AdditionalCompanyDocuments []AdditionalCompanyDocuments `gorm:"foreignKey:KYBID;constraint:OnDelete:CASCADE;" json:"additional_company_documents,omitempty"`

	Status constants.VerificationStatus `json:"status"`
}

type BusinessDetails struct {
	Entity
	BusinessAddress    string `json:"business_address"`
	CompanyTradingName string `json:"company_trading_name"`
	BusinessActivity   string `json:"business_activity"`
	BusinessPartners   string `json:"business_partners"`
	BusinessWebsite    string `json:"business_website"`
	BusinessLocation   string `json:"business_location"`
}

type FinancialDetails struct {
	Entity
	Purpose              string `json:"purpose"`                // Purpose of the payment account
	Sectors              string `json:"sectors"`                // Target business sectors
	PaymentFromCountries string `json:"payment_from_countries"` // Possibly comma-separated or list
	PaymentToCountries   string `json:"payment_to_countries"`
	IncomeEUR            int    `json:"income_eur"`             // Expected income transactions in EUR
	TxCount              int    `json:"tx_count"`               // Expected transactions number
	AvgTxAmount          int    `json:"avg_tx_amount"`          // Avg. amount of a single transaction
	OutgoingTxCount      int    `json:"outgoing_tx_count"`      // Expected outgoing transactions number
	AvgOutgoingTxAmount  int    `json:"avg_outgoing_tx_amount"` // Avg. amount of a single outgoing transaction
}

type AdditionalCompanyDocuments struct {
	Entity
	KYBID       uint      `json:"-"`
	DocumentURL string    `json:"document_url"`
	UploadedAt  time.Time `json:"uploaded_at"`
}
