package models

import (
	"auth-service/internal/constants"
	"time"
)

type Profile struct {
	Entity

	CompanyID uint    `gorm:"not null"`
	Company   Company `gorm:"foreignKey:CompanyID;constraint:OnDelete:CASCADE;"`

	Name               string                `json:"name"`
	Email              string                `json:"email"`
	PhoneNumber        string                `json:"phone_number"`
	Address            string                `json:"address"`
	DateOfBirth        time.Time             `json:"date_of_birth"`
	Password           string                `json:"password"`
	Role               constants.CompanyRole `gorm:"default:owner" json:"role"`
	AccountPermissions []AccountPermission   `gorm:"foreignKey:ProfileID" json:"account_permissions"`
}
