package models

import (
	"auth-service/internal/constants"
	"time"
)

type KYC struct {
	Entity
	CompanyID        uint
	CompanyDetailsID uint
	RepresentativeID uint

	Company        *Company                     `gorm:"foreignKey:CompanyID;constraint:OnDelete:CASCADE;" json:"-"`
	CompanyDetails CompanyDetails               `gorm:"foreignKey:CompanyDetailsID;constraint:OnDelete:CASCADE;"` // Этап 1 KYC
	Representative Representative               `gorm:"foreignKey:RepresentativeID;constraint:OnDelete:CASCADE;"` // Этап 2 KYC
	Status         constants.VerificationStatus `json:"status"`                                                   // "pending", "verified", "rejected, in_review"
}

type CompanyDetails struct {
	Entity
	CompanyName        string         `json:"company_name"`
	Country            string         `json:"country"`
	RegistrationNumber string         `json:"registration_number"`
	TypeOfEntity       string         `json:"type_of_entity"`
	LegalAddress       string         `json:"legal_address"`
	City               string         `json:"city"`
	Website            string         `json:"website"`
	CompanyEmail       string         `json:"company_email"`
	CompanyPhone       string         `json:"company_phone"`
	Date               time.Time      `json:"date"`
	Documents          []KYCDocuments `gorm:"foreignKey:CompanyDetailsID;constraint:OnDelete:CASCADE;"`
}

type Representative struct {
	Entity
	FirstName     string `json:"first_name"`
	LastName      string `json:"last_name"`
	Email         string `json:"email"`
	ContactNumber string `json:"contact_number"`
	CompanyRole   string `json:"company_role"`
}

type KYCDocuments struct {
	Entity
	CompanyDetailsID uint
	DocumentURL      string    `json:"document_url"`
	UploadedAt       time.Time `json:"uploaded_At"`
}
