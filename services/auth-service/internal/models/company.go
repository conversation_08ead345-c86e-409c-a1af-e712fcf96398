package models

import "auth-service/internal/constants"

type Company struct {
	Entity
	CompanyTradingName string                           `gorm:"not null" json:"company_trading_name"`
	Country            string                           `gorm:"not null" json:"country"`
	ContactPhone       string                           `gorm:"not null" json:"contact_phone"`
	Email              string                           `gorm:"unique;not null" json:"email"`
	Password           string                           `gorm:"not null" json:"password"`
	SelectedProduct    constants.ProductType            `gorm:"not null" json:"selected_product"` // Banking Services, Payment Services
	EmailVerified      bool                             `gorm:"default:false" json:"email_verified"`
	VerificationStatus constants.UserVerificationStatus `gorm:"default:pending" json:"verification_status"` // pending, verified, blocked
	Profiles           []Profile                        `gorm:"foreignKey:CompanyID"`

	KYC *KYC `gorm:"foreignKey:CompanyID"`
	Kyb *Kyb `gorm:"foreignKey:CompanyID"`
}
