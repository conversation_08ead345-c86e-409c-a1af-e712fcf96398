package parsers

import (
	"fmt"
	"time"
)

type DateOnly time.Time

const layout = "2006-01-02"

func (d *DateOnly) UnmarshalJSON(b []byte) error {
	s := string(b)
	// Удаляем кавычки
	s = s[1 : len(s)-1]

	t, err := time.Parse(layout, s)
	if err != nil {
		return fmt.Errorf("invalid date format (expected yyyy-MM-dd): %w", err)
	}

	*d = DateOnly(t)
	return nil
}

func (d DateOnly) ToTime() time.Time {
	return time.Time(d)
}
