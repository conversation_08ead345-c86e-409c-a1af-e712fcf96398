package utils

import (
	"auth-service/core/config"
	"errors"
	"fmt"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

type Email struct {
	SenderName       string
	SenderEmail      string
	RecipientEmail   string
	Subject          string
	HtmlContent      string
	PlainTextContent string
	VerificationCode string
}

func SendEmail(input Email) error {
	from := mail.NewEmail(input.SenderName, input.SenderEmail)
	to := mail.NewEmail("", input.RecipientEmail)
	message := mail.NewSingleEmail(from, input.Subject, to, input.PlainTextContent, input.HtmlContent)

	client := sendgrid.NewSendClient(config.Get().SendGrid.ApiKey)

	_, err := client.Send(message)
	if err != nil {
		return fmt.Errorf("ошибка отправки письма через SendGrid: %v", err)
	}

	return nil
}

func SendVerificationCodeToEmail(email string, verificationCode string) error {
	conf := config.Get()
	link := fmt.Sprintf("%s/api/v1/company/verify?code=%s", conf.Server.BaseUrl, verificationCode)

	htmlContent, err := RenderTemplate("verification_email.html", map[string]string{
		"Link": link,
	})
	if err != nil {
		return fmt.Errorf("ошибка при рендеринге email шаблона: %w", err)
	}

	emailS := Email{
		SenderName:       conf.SendGrid.SenderName,
		SenderEmail:      conf.SendGrid.SenderEmail,
		RecipientEmail:   email,
		Subject:          "Подтверждение регистрации",
		HtmlContent:      htmlContent,
		PlainTextContent: fmt.Sprintf("Перейдите по ссылке, чтобы подтвердить email: %s", link),
	}

	if err := SendEmail(emailS); err != nil {
		return errors.New("ошибка при отправке письма")
	}
	return nil
}
