package utils

import (
	"bytes"
	"html/template"
	"path/filepath"
)

func RenderTemplate(templateName string, data interface{}) (string, error) {
	path := filepath.Join("pkg", "templates", templateName)

	tmpl, err := template.ParseFiles(path)
	if err != nil {
		return "", err
	}

	var tpl bytes.Buffer
	if err := tmpl.Execute(&tpl, data); err != nil {
		return "", err
	}

	return tpl.String(), nil
}
