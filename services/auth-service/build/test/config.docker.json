{"server": {"host": "0.0.0.0", "port": 8091, "baseURL": "https://back.monexa.uk", "frontUrl": "https://monexa.uk"}, "database": {"host": "auth-postgres-db", "port": 5434, "name": "monexa_db", "username": "postgres", "password": "fafQWllRTl4nfs", "sslMode": "disable"}, "transactionService": {"host": "http://**************:8081/"}, "rabbitMQ": {"host": "monexa-rabbitmq", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "token": {"secretKey": "aafsdfwe23rfasdvrg9fvjwfwu4832k2masfgjouergrg"}, "redis": {"address": "redis:6379", "db": 0, "password": "dfff34#2pl#ghl"}, "sendGrid": {"apiKey": "*********************************************************************", "senderEmail": "<EMAIL>", "senderName": ""}}