version: '3.7'

services:
  auth_service:
    container_name: auth-service
    build:
      context: ../../
      dockerfile: build/test/Dockerfile
    restart: always
    ports:
      - "8091:8091"
    volumes:
      - ../../logs:/app/logs

    depends_on:
      - auth_postgres_db


    command: ["./api"]
    networks:
      - test_infrastructure_network

  auth_postgres_db:
    image: postgres:latest
    container_name: auth-postgres-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=fafQWllRTl4nfs
      - POSTGRES_DB=monexa_db
    ports:
      - "5434:5434"
    volumes:
      - monexa_auth_postgres_data:/var/lib/postgresql/data
    networks:
      - test_infrastructure_network
    command: -p 5434

  auth_seed:
    container_name: auth-seed
    build:
      context: ../../
      dockerfile: build/test/Dockerfile
    restart: on-failure
    depends_on:
      - auth_postgres_db
    command: ["./seeder"]
    networks:
      - test_infrastructure_network
#
#  promtail:
#    image: grafana/promtail:2.7.1
#    container_name: promtail
#    volumes:
#      - ../../logs:/var/log
#      - ./promtail-config.yaml:/etc/promtail/promtail.yaml
#    command: -config.file=/etc/promtail/promtail.yaml
#    networks:
#      - default


volumes:
  monexa_auth_postgres_data:
    driver: local


networks:
  default:
    name: monexa_auth_network
  test_infrastructure_network:
    external: true

