version: '3.7'

services:
  auth_service_local:
    container_name: auth_service_local
    build:
      context: ../../
      dockerfile: build/local/Dockerfile
    restart: always
    ports:
      - "8095:8095"
    volumes:
      - ../../logs:/app/logs

    depends_on:
      - auth_postgres_db_local

    command: ["./api"]
    networks:
      - infrastructure_network

  auth_postgres_db_local:

    image: postgres:latest
    container_name: auth_postgres_db_local
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=fafQWllRTl4nfs
      - POSTGRES_DB=monexa_db
    ports:
      - "5435:5435"
    volumes:
      - monexa_auth_postgres_data_local:/var/lib/postgresql/data
    networks:
      - infrastructure_network
    command: -p 5435

  auth_seed_local:
    container_name: auth_seed_local
    build:
      context: ../../
      dockerfile: build/local/Dockerfile
    restart: on-failure
    depends_on:
      - auth_postgres_db_local

    command: ["./seeder"]
    networks:
      - infrastructure_network

#  promtail:
#    image: grafana/promtail:2.7.1
#    container_name: promtail
#    volumes:
#      - ../../logs:/var/log
#      - ./promtail-config.yaml:/etc/promtail/promtail.yaml
#    command: -config.file=/etc/promtail/promtail.yaml
#    networks:
#      - local_infrastructure_network


volumes:
  monexa_auth_postgres_data_local:
    driver: local

networks:
  infrastructure_network:
    external: true
    name: test_infrastructure_network
