{"server": {"host": "0.0.0.0", "port": 8095, "frontUrl": "http://localhost:5174"}, "transactionService": {"host": "http://localhost:9091"}, "database": {"host": "auth_postgres_db_local", "port": 5435, "name": "monexa_db", "username": "postgres", "password": "fafQWllRTl4nfs", "sslMode": "disable"}, "rabbitMQ": {"host": "monexa-rabbitmq", "port": 5672, "user": "monexa-user", "password": "sd23famlredqui"}, "dir": {"seeder": "/app/fixtures", "logPath": "./logs/app.log"}, "token": {"secretKey": "aafsdfwe23rfasdvrg9fvjwfwu4832k2masfgjouergrg"}, "redis": {"address": "redis:6379", "db": 0, "password": "dfff34#2pl#ghl"}, "sendGrid": {"apiKey": "*********************************************************************", "senderEmail": "<EMAIL>", "senderName": ""}}