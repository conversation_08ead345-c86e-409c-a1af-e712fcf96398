package main

import (
	"auth-service/core/config"
	"auth-service/core/connect"
	"auth-service/core/database"
	"auth-service/core/redis"
	"auth-service/core/server"
	"flag"
	"fmt"
	"strings"
)

type flags []string

func (f *flags) String() string {
	return strings.Join(*f, ", ")
}

func (f *flags) Set(value string) error {
	*f = append(*f, value)
	return nil
}

func main() {
	var sources flags

	flag.Var(&sources, "s", "Specify the source for seeding")
	flag.Parse()

	conf, err := config.Load()
	if err != nil {
		fmt.Printf("err config.Load() %s\n", err)
		return
	}
	fmt.Println(conf.Database.Password)
	dbase, err := database.Connect(conf.Database)
	if err != nil {
		fmt.Printf("err db.Connect() %s\n", err)
		return
	}
	connect.DB = dbase
	rds := redis.New(conf.Redis)
	connect.Redis = rds

	server.Runner(sources)
}
