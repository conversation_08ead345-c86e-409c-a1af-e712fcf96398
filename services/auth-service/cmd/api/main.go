package main

import (
	"auth-service/core/connect"
	"auth-service/core/server"
	"context"
	"go.uber.org/zap"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	s := server.Server{}
	time.Sleep(10 * time.Second)
	r, _, e := s.Init()
	if e != nil {
		panic(e.Error())
	}
	// Run worker in a goroutine
	//go func() {
	//	if err := rabHandler.Start(ctx); err != nil {
	//		connect.Logger.Fatal("Worker encountered an error", zap.Error(err))
	//	}
	//}()

	s.Run(r)
	<-ctx.Done()

	stop()
	connect.Logger.Info("Shutting down gracefully, press Ctrl+C again to force")

	connect.Logger.Info("Grpc server down gracefully")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := s.Srv.Shutdown(ctx); err != nil {
		connect.Logger.Fatal("Server forced to shutdown:", zap.Error(err))
	}
	connect.Logger.Fatal("Server exiting")
}
