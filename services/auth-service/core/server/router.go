package server

import (
	"auth-service/internal/controllers"
	middleware2 "auth-service/internal/middleware"
	"auth-service/internal/services"
	"github.com/elliotchance/orderedmap"
	"github.com/gin-gonic/gin"
)

type Controller interface {
	Register(r *gin.RouterGroup, s string) *gin.RouterGroup
}

func SetupRoutesWithDeps(service *services.Service) *gin.Engine {

	e := gin.Default()
	e.MaxMultipartMemory = 8 << 20
	e.Use(middleware2.CORSMiddleware(), middleware2.Log)
	auth := e.Group("/api/v1")
	{
		r := orderedmap.NewOrderedMap()
		r.Set("user", controllers.NewUserController(service.UserServiceInterface))
		r.Set("company", controllers.NewCompanyController(service.CompanyServiceInterface, service.ProfileServiceInterface))
		r.Set("kyc", controllers.NewKYCController(service.VerificationCompanyServiceInterface, service.UserServiceInterface, service.ProfileServiceInterface))
		r.Set("kyb", controllers.NewKYBController(service.VerificationCompanyServiceInterface, service.UserServiceInterface, service.ProfileServiceInterface))
		r.Set("profile", controllers.NewProfileController(service.ProfileServiceInterface, service.UserServiceInterface))

		for g := r.Front(); g != nil; g = g.Next() {
			if c, ok := g.Value.(Controller); ok {
				c.Register(auth, g.Key.(string))
			}
		}
	}

	//mediaPath := config.Get().Dir.Media
	//e.Static("/media", mediaPath)

	return e
}
