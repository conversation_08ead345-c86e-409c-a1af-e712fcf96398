package server

import (
	"auth-service/core/config"
	"auth-service/core/connect"
	"auth-service/core/database"
	"auth-service/core/rabbitmq"
	"auth-service/core/redis"
	"auth-service/core/worker"
	"auth-service/internal/repositories"
	"auth-service/internal/services"
	"auth-service/pkg/logger"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
)

type Server struct {
	Srv  *http.Server
	Test bool
}

func (s *Server) Init() (*gin.Engine, *worker.RabbitMQHandler, error) {
	conf, err := config.Load()
	if err != nil {
		fmt.Printf("err config.Load() %s\n", err)
		return nil, nil, err
	}
	logger.Init("app.log")
	fmt.Println(conf.Database.Password)
	fmt.Println(conf.Database.Host)
	fmt.Println(conf.Database.Port)
	fmt.Println(conf.Database.Name)
	//postgres
	dbase, err := database.Connect(conf.Database)
	if err != nil {
		fmt.Printf("err db.Connect() %s\n", err)
		return nil, nil, err
	}

	//init redis
	rds := redis.New(conf.Redis)
	//init entities
	repo := repositories.NewRepository(dbase, rds)
	service := services.NewService(repo, *conf)

	//init rabbitmq
	rab := rabbitmq.NewRabbitMq()
	err = rab.Init(conf.RabbitMQ)
	if err != nil {
		fmt.Println(err.Error())
		fmt.Printf("err rabbitmq connect %s\n", err)
	}

	//init real logger
	loggerr, err := logger.NewLogger(config.Get().Dir.LogPath)
	if err != nil {
		fmt.Printf("error init log zap")
	}
	defer loggerr.Sync()

	//rabHandler := worker.NewRabbitMQHandler(service, rab, loggerr)

	connect.DB = dbase
	connect.Redis = rds
	connect.RabbitMQ = rab
	connect.Logger = loggerr
	//routing api
	r := SetupRoutesWithDeps(service)

	return r, nil, nil
}

func (s *Server) Run(r *gin.Engine) {
	if r == nil {
		panic("Server not initialized")
	}

	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", config.Get().Server.Host, config.Get().Server.Port),
		Handler: r,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			connect.Logger.Fatal("Server error while running", zap.Error(err))
			panic(err.Error())
		}
	}()

	s.Srv = srv
}

func CloseDb(db2 *gorm.DB) {
	sql, err := db2.DB()
	if err != nil {
		fmt.Printf("error get sql db %s\n", err)
		return
	}

	err = sql.Close()
	if err != nil {
		fmt.Printf("error sql db close %s\n", err)
		return
	}
}

func (s *Server) CloseAll() {
	d := connect.DB
	CloseDb(d)
}
