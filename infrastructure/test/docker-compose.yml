version: '3.7'

services:
  rabbitmq:
    image: rabbitmq:management
    container_name: monexa-rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=monexa-user
      - RABBITMQ_DEFAULT_PASS=sd23famlredqui
      - RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS=-rabbit heartbeat 30
    volumes:
      - monexa_rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "5673:5673"
      - "15672:15672"
      - "15674:15674"
      - "15671:15671"
    networks:
      - test_infrastructure_network

  redis:
    image: redis
    container_name: redis
    environment:
      - REDIS_PASSWORD=dfff34#2pl#ghl
    restart: always
    ports:
      - "6379:6379"
    command: [ "redis-server", "--bind", "0.0.0.0", "--protected-mode", "no", "--requirepass", "dfff34#2pl#ghl" ]
    volumes:
      - monexa_redis_data:/data
    networks:
      - test_infrastructure_network

#  prometheus:
#    image: prom/prometheus:latest
#    container_name: prometheus
#    volumes:
#      - ../metrics/prometheus.yml:/etc/prometheus/prometheus.yml
#    ports:
#      - "9090:9090"
#    networks:
#      - default

#  loki:
#    image: grafana/loki:latest
#    container_name: loki
#    command: "-config.file=/etc/loki/config.yaml -log.level=debug"
#    ports:
#      - "3100:3100"
#    volumes:
#      - ../metrics/loki-config.yaml:/etc/loki/config.yaml
#      - ./loki_data:/tmp/loki
#
#    networks:
#      - default


#  grafana:
#    image: grafana/grafana:latest
#    container_name: grafana
#    ports:
#      - "3000:3000"
#    environment:
#      - GF_SECURITY_ADMIN_PASSWORD=admin
#    volumes:
#      - grafana_data:/var/lib/grafana
#    depends_on:
#      - prometheus
#      - loki
#    networks:
#      - default

#  cadvisor:
#    container_name: cadvisor
#    image: gcr.io/cadvisor/cadvisor
#    ports:
#      - "8080:8080"
#    volumes:
#      - /:/rootfs:ro
#      - /var/run:/var/run:ro
#      - /sys:/sys:ro
#      - /var/lib/docker/:/var/lib/docker:ro
#      - /var/run/docker.sock:/var/run/docker.sock:ro # Add only if you have your containers running on Mac
#    networks:
#      - default


volumes:
  monexa_rabbitmq_data:
    driver: local
  monexa_redis_data:
    driver: local
#  grafana_data:
#    driver: local


networks:
  test_infrastructure_network:
    name: test_infrastructure_network