#!/bin/bash
set -e

echo "🛠 Updating test infrastructure services..."

# Array of compose file and services
compose_files=(
  "infrastructure/test/docker-compose.yml rabbitmq redis"
  "services/auth-service/build/test/docker-compose.yml auth_postgres_db auth_service"
  "services/notification-service/build/test/docker-compose.yml notification_service notification_mongodb"
  "services/transaction-service/build/test/docker-compose.yml transaction_service transaction_postgres_db"
)

# Function to wait for Redis to be healthy
wait_for_redis() {
  echo "⏳ Waiting for Redis to be ready..."
  for i in {1..20}; do
    if docker exec redis redis-cli -a dfff34#2pl#ghl ping | grep -q PONG; then
      echo "✅ Redis is ready!"
      return
    fi
    sleep 1
  done
  echo "❌ Redis did not respond in time"
  exit 1
}

# Iterate and update each group
for entry in "${compose_files[@]}"; do
  compose_file=$(echo "$entry" | awk '{print $1}')
  services=$(echo "$entry" | cut -d' ' -f2-)

  echo "🚀 Bringing up services: $services from $compose_file"
  docker compose -f "$compose_file" up -d --no-deps --build $services

  # Check if redis is part of current services
  if echo "$services" | grep -q "\<redis\>"; then
    wait_for_redis
  fi
done

echo "✅ All specified services have been updated."